lockfileVersion: '9.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:

  .:
    dependencies:
      '@angular/animations':
        specifier: ^16.2.0
        version: 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      '@angular/cdk':
        specifier: 16.2.14
        version: 16.2.14(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/common':
        specifier: ^16.2.0
        version: 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/compiler':
        specifier: ^16.2.0
        version: 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      '@angular/core':
        specifier: ^16.2.0
        version: 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@angular/forms':
        specifier: ^16.2.0
        version: 16.2.12(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(rxjs@7.8.1)
      '@angular/material':
        specifier: 16.2.14
        version: 16.2.14(dba6c3a62b3e5c2bbd117b00518804f3)
      '@angular/platform-browser':
        specifier: ^16.2.0
        version: 16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      '@angular/platform-browser-dynamic':
        specifier: ^16.2.0
        version: 16.2.12(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))
      '@angular/platform-server':
        specifier: ^16.2.0
        version: 16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))
      '@angular/router':
        specifier: ^16.2.0
        version: 16.2.12(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(rxjs@7.8.1)
      '@nguniversal/express-engine':
        specifier: ^16.2.0
        version: 16.2.0(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(express@4.18.2)
      angular-calendar:
        specifier: ^0.31.1
        version: 0.31.1(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      chart.js:
        specifier: ^3.8.0
        version: 3.9.1
      date-fns:
        specifier: ^3.6.0
        version: 3.6.0
      express:
        specifier: ^4.15.2
        version: 4.18.2
      gsap:
        specifier: ^3.13.0
        version: 3.13.0
      icon:
        specifier: link:@angular/material/icon
        version: link:@angular/material/icon
      ng2-charts:
        specifier: ^3.1.0
        version: 3.1.2(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(chart.js@3.9.1)(rxjs@7.8.1)
      ngx-bootstrap:
        specifier: ^19.0.2
        version: 19.0.2(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/forms@16.2.12(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(rxjs@7.8.1))(rxjs@7.8.1)
      ngx-loading:
        specifier: ^17.0.0
        version: 17.0.0(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      ngx-pagination:
        specifier: ^6.0.3
        version: 6.0.3(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      ngx-toastr:
        specifier: ^19.0.0
        version: 19.0.0(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))
      rxjs:
        specifier: ~7.8.0
        version: 7.8.1
      tslib:
        specifier: ^2.3.0
        version: 2.6.2
      zone.js:
        specifier: ~0.13.0
        version: 0.13.3
    devDependencies:
      '@angular-devkit/build-angular':
        specifier: ^16.2.14
        version: 16.2.14(@angular/compiler-cli@16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6))(@angular/platform-server@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))))(@types/node@16.18.97)(karma@6.4.2)(typescript@5.1.6)
      '@angular/cli':
        specifier: ^16.2.14
        version: 16.2.14(chokidar@3.5.3)
      '@angular/compiler-cli':
        specifier: ^16.2.0
        version: 16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6)
      '@nguniversal/builders':
        specifier: ^16.2.0
        version: 16.2.0(@angular-devkit/build-angular@16.2.14(@angular/compiler-cli@16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6))(@angular/platform-server@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))))(@types/node@16.18.97)(karma@6.4.2)(typescript@5.1.6))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@types/express@4.17.21)(chokidar@3.5.3)(typescript@5.1.6)
      '@types/express':
        specifier: ^4.17.0
        version: 4.17.21
      '@types/jasmine':
        specifier: ~4.3.0
        version: 4.3.6
      '@types/node':
        specifier: ^16.11.7
        version: 16.18.97
      jasmine-core:
        specifier: ~4.6.0
        version: 4.6.0
      karma:
        specifier: ~6.4.0
        version: 6.4.2
      karma-chrome-launcher:
        specifier: ~3.2.0
        version: 3.2.0
      karma-coverage:
        specifier: ~2.2.0
        version: 2.2.1
      karma-jasmine:
        specifier: ~5.1.0
        version: 5.1.0(karma@6.4.2)
      karma-jasmine-html-reporter:
        specifier: ~2.1.0
        version: 2.1.0(jasmine-core@4.6.0)(karma-jasmine@5.1.0(karma@6.4.2))(karma@6.4.2)
      typescript:
        specifier: ~5.1.3
        version: 5.1.6

packages:

  '@ampproject/remapping@2.2.1':
    resolution: {integrity: sha512-lFMjJTrFL3j7L9yBxwYfCq2k6qqwHyzuUl/XBnif78PWTJYyL/dfowQHWE3sp6U6ZzqWiiIZnpTMO96zhkjwtg==}
    engines: {node: '>=6.0.0'}

  '@angular-devkit/architect@0.1602.11':
    resolution: {integrity: sha512-qC1tPL/82gxqCS1z9pTpLn5NQH6uqbV6UNjbkFEQpTwEyWEK6VLChAJsybHHfbpssPS2HWf31VoUzX7RqDjoQQ==}
    engines: {node: ^16.14.0 || >=18.10.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}

  '@angular-devkit/architect@0.1602.14':
    resolution: {integrity: sha512-eSdONEV5dbtLNiOMBy9Ue9DdJ1ct6dH9RdZfYiedq6VZn0lejePAjY36MYVXgq2jTE+v/uIiaNy7caea5pt55A==}
    engines: {node: ^16.14.0 || >=18.10.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}

  '@angular-devkit/build-angular@16.2.14':
    resolution: {integrity: sha512-bXQ6i7QPhwmYHuh+DSNkBhjTIHQF0C6fqZEg2ApJA3NmnzE98oQnmJ9AnGnAkdf1Mjn3xi2gxoZWPDDxGEINMw==}
    engines: {node: ^16.14.0 || >=18.10.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    peerDependencies:
      '@angular/compiler-cli': ^16.0.0
      '@angular/localize': ^16.0.0
      '@angular/platform-server': ^16.0.0
      '@angular/service-worker': ^16.0.0
      jest: ^29.5.0
      jest-environment-jsdom: ^29.5.0
      karma: ^6.3.0
      ng-packagr: ^16.0.0
      protractor: ^7.0.0
      tailwindcss: ^2.0.0 || ^3.0.0
      typescript: '>=4.9.3 <5.2'
    peerDependenciesMeta:
      '@angular/localize':
        optional: true
      '@angular/platform-server':
        optional: true
      '@angular/service-worker':
        optional: true
      jest:
        optional: true
      jest-environment-jsdom:
        optional: true
      karma:
        optional: true
      ng-packagr:
        optional: true
      protractor:
        optional: true
      tailwindcss:
        optional: true

  '@angular-devkit/build-webpack@0.1602.14':
    resolution: {integrity: sha512-f+ZTCjOoA1SCQEaX3L/63ubqr/vlHkwDXAtKjBsQgyz6srnETcjy96Us5k/LoK7/hPc85zFneqLinfqOMVWHJQ==}
    engines: {node: ^16.14.0 || >=18.10.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    peerDependencies:
      webpack: ^5.30.0
      webpack-dev-server: ^4.0.0

  '@angular-devkit/core@16.2.11':
    resolution: {integrity: sha512-u3cEQHqhSMWyAFIaPdRukCJwEUJt7Fy3C02gTlTeCB4F/OnftVFIm2e5vmCqMo9rgbfdvjWj9V+7wWiCpMrzAQ==}
    engines: {node: ^16.14.0 || >=18.10.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    peerDependencies:
      chokidar: ^3.5.2
    peerDependenciesMeta:
      chokidar:
        optional: true

  '@angular-devkit/core@16.2.14':
    resolution: {integrity: sha512-Ui14/d2+p7lnmXlK/AX2ieQEGInBV75lonNtPQgwrYgskF8ufCuN0DyVZQUy9fJDkC+xQxbJyYrby/BS0R0e7w==}
    engines: {node: ^16.14.0 || >=18.10.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    peerDependencies:
      chokidar: ^3.5.2
    peerDependenciesMeta:
      chokidar:
        optional: true

  '@angular-devkit/schematics@16.2.14':
    resolution: {integrity: sha512-B6LQKInCT8w5zx5Pbroext5eFFRTCJdTwHN8GhcVS8IeKCnkeqVTQLjB4lBUg7LEm8Y7UHXwzrVxmk+f+MBXhw==}
    engines: {node: ^16.14.0 || >=18.10.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}

  '@angular/animations@16.2.12':
    resolution: {integrity: sha512-MD0ElviEfAJY8qMOd6/jjSSvtqER2RDAi0lxe6EtUacC1DHCYkaPrKW4vLqY+tmZBg1yf+6n+uS77pXcHHcA3w==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular/core': 16.2.12

  '@angular/cdk@16.2.14':
    resolution: {integrity: sha512-n6PrGdiVeSTEmM/HEiwIyg6YQUUymZrb5afaNLGFRM5YL0Y8OBqd+XhCjb0OfD/AfgCUtedVEPwNqrfW8KzgGw==}
    peerDependencies:
      '@angular/common': ^16.0.0 || ^17.0.0
      '@angular/core': ^16.0.0 || ^17.0.0
      rxjs: ^6.5.3 || ^7.4.0

  '@angular/cli@16.2.14':
    resolution: {integrity: sha512-0y71jtitigVolm4Rim1b8xPQ+B22cGp4Spef2Wunpqj67UowN6tsZaVuWBEQh4u5xauX8LAHKqsvy37ZPWCc4A==}
    engines: {node: ^16.14.0 || >=18.10.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    hasBin: true

  '@angular/common@16.2.12':
    resolution: {integrity: sha512-B+WY/cT2VgEaz9HfJitBmgdk4I333XG/ybC98CMC4Wz8E49T8yzivmmxXB3OD6qvjcOB6ftuicl6WBqLbZNg2w==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular/core': 16.2.12
      rxjs: ^6.5.3 || ^7.4.0

  '@angular/compiler-cli@16.2.12':
    resolution: {integrity: sha512-pWSrr152562ujh6lsFZR8NfNc5Ljj+zSTQO44DsuB0tZjwEpnRcjJEgzuhGXr+CoiBf+jTSPZKemtSktDk5aaA==}
    engines: {node: ^16.14.0 || >=18.10.0}
    hasBin: true
    peerDependencies:
      '@angular/compiler': 16.2.12
      typescript: '>=4.9.3 <5.2'

  '@angular/compiler@16.2.12':
    resolution: {integrity: sha512-6SMXUgSVekGM7R6l1Z9rCtUGtlg58GFmgbpMCsGf+VXxP468Njw8rjT2YZkf5aEPxEuRpSHhDYjqz7n14cwCXQ==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular/core': 16.2.12
    peerDependenciesMeta:
      '@angular/core':
        optional: true

  '@angular/core@16.2.12':
    resolution: {integrity: sha512-GLLlDeke/NjroaLYOks0uyzFVo6HyLl7VOm0K1QpLXnYvW63W9Ql/T3yguRZa7tRkOAeFZ3jw+1wnBD4O8MoUA==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      rxjs: ^6.5.3 || ^7.4.0
      zone.js: ~0.13.0

  '@angular/forms@16.2.12':
    resolution: {integrity: sha512-1Eao89hlBgLR3v8tU91vccn21BBKL06WWxl7zLpQmG6Hun+2jrThgOE4Pf3os4fkkbH4Apj0tWL2fNIWe/blbw==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular/common': 16.2.12
      '@angular/core': 16.2.12
      '@angular/platform-browser': 16.2.12
      rxjs: ^6.5.3 || ^7.4.0

  '@angular/material@16.2.14':
    resolution: {integrity: sha512-zQIxUb23elPfiIvddqkIDYqQhAHa9ZwMblfbv+ug8bxr4D0Dw360jIarxCgMjAcLj7Ccl3GBqZMUnVeM6cjthw==}
    peerDependencies:
      '@angular/animations': ^16.0.0 || ^17.0.0
      '@angular/cdk': 16.2.14
      '@angular/common': ^16.0.0 || ^17.0.0
      '@angular/core': ^16.0.0 || ^17.0.0
      '@angular/forms': ^16.0.0 || ^17.0.0
      '@angular/platform-browser': ^16.0.0 || ^17.0.0
      rxjs: ^6.5.3 || ^7.4.0

  '@angular/platform-browser-dynamic@16.2.12':
    resolution: {integrity: sha512-ya54jerNgreCVAR278wZavwjrUWImMr2F8yM5n9HBvsMBbFaAQ83anwbOEiHEF2BlR+gJiEBLfpuPRMw20pHqw==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular/common': 16.2.12
      '@angular/compiler': 16.2.12
      '@angular/core': 16.2.12
      '@angular/platform-browser': 16.2.12

  '@angular/platform-browser@16.2.12':
    resolution: {integrity: sha512-NnH7ju1iirmVEsUq432DTm0nZBGQsBrU40M3ZeVHMQ2subnGiyUs3QyzDz8+VWLL/T5xTxWLt9BkDn65vgzlIQ==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular/animations': 16.2.12
      '@angular/common': 16.2.12
      '@angular/core': 16.2.12
    peerDependenciesMeta:
      '@angular/animations':
        optional: true

  '@angular/platform-server@16.2.12':
    resolution: {integrity: sha512-IKuDYk54ZOc/Ic39hY1nlPGfCNVGdxsV0FMcuuVrqKQ2pKjeDPkfKIDBZB5vje7P08JKIKgHmEhb3XKe2D7HoQ==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular/animations': 16.2.12
      '@angular/common': 16.2.12
      '@angular/compiler': 16.2.12
      '@angular/core': 16.2.12
      '@angular/platform-browser': 16.2.12

  '@angular/router@16.2.12':
    resolution: {integrity: sha512-aU6QnYSza005V9P3W6PpkieL56O0IHps96DjqI1RS8yOJUl3THmokqYN4Fm5+HXy4f390FN9i6ftadYQDKeWmA==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular/common': 16.2.12
      '@angular/core': 16.2.12
      '@angular/platform-browser': 16.2.12
      rxjs: ^6.5.3 || ^7.4.0

  '@assemblyscript/loader@0.10.1':
    resolution: {integrity: sha512-H71nDOOL8Y7kWRLqf6Sums+01Q5msqBW2KhDUTemh1tvY04eSkSXrK0uj/4mmY0Xr16/3zyZmsrxN7CKuRbNRg==}

  '@babel/code-frame@7.23.5':
    resolution: {integrity: sha512-CgH3s1a96LipHCmSUmYFPwY7MNx8C3avkq7i4Wl3cfa662ldtUe4VM1TPXX70pfmrlWTb6jLqTYrZyT2ZTJBgA==}
    engines: {node: '>=6.9.0'}

  '@babel/compat-data@7.23.5':
    resolution: {integrity: sha512-uU27kfDRlhfKl+w1U6vp16IuvSLtjAxdArVXPa9BvLkrr7CYIsxH5adpHObeAGY/41+syctUWOZ140a2Rvkgjw==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.22.9':
    resolution: {integrity: sha512-G2EgeufBcYw27U4hhoIwFcgc1XU7TlXJ3mv04oOv1WCuo900U/anZSPzEqNjwdjgffkk2Gs0AN0dW1CKVLcG7w==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.23.2':
    resolution: {integrity: sha512-n7s51eWdaWZ3vGT2tD4T7J6eJs3QoBXydv7vkUM06Bf1cbVD2Kc2UrkzhiQwobfV7NwOnQXYL7UBJ5VPU+RGoQ==}
    engines: {node: '>=6.9.0'}

  '@babel/core@7.23.6':
    resolution: {integrity: sha512-FxpRyGjrMJXh7X3wGLGhNDCRiwpWEF74sKjTLDJSG5Kyvow3QZaG0Adbqzi9ZrVjTWpsX+2cxWXD71NMg93kdw==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.22.9':
    resolution: {integrity: sha512-KtLMbmicyuK2Ak/FTCJVbDnkN1SlT8/kceFTiuDiiRUUSMnHMidxSCdG4ndkTOHHpoomWe/4xkvHkEOncwjYIw==}
    engines: {node: '>=6.9.0'}

  '@babel/generator@7.23.6':
    resolution: {integrity: sha512-qrSfCYxYQB5owCmGLbl8XRpX1ytXlpueOb0N0UmQwA073KZxejgQTzAmJezxvpwQD9uGtK2shHdi55QT+MbjIw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-annotate-as-pure@7.22.5':
    resolution: {integrity: sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-builder-binary-assignment-operator-visitor@7.22.15':
    resolution: {integrity: sha512-QkBXwGgaoC2GtGZRoma6kv7Szfv06khvhFav67ZExau2RaXzy8MpHSMO2PNoP2XtmQphJQRHFfg77Bq731Yizw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-compilation-targets@7.23.6':
    resolution: {integrity: sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-create-class-features-plugin@7.23.6':
    resolution: {integrity: sha512-cBXU1vZni/CpGF29iTu4YRbOZt3Wat6zCoMDxRF1MayiEc4URxOj31tT65HUM0CRpMowA3HCJaAOVOUnMf96cw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-create-regexp-features-plugin@7.22.15':
    resolution: {integrity: sha512-29FkPLFjn4TPEa3RE7GpW+qbE8tlsu3jntNYNfcGsc49LphF1PQIiD+vMZ1z1xVOKt+93khA9tc2JBs3kBjA7w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-define-polyfill-provider@0.4.4':
    resolution: {integrity: sha512-QcJMILQCu2jm5TFPGA3lCpJJTeEP+mqeXooG/NZbg/h5FTFi6V0+99ahlRsW8/kRLyb24LZVCCiclDedhLKcBA==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  '@babel/helper-environment-visitor@7.22.20':
    resolution: {integrity: sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-function-name@7.23.0':
    resolution: {integrity: sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-hoist-variables@7.22.5':
    resolution: {integrity: sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-member-expression-to-functions@7.23.0':
    resolution: {integrity: sha512-6gfrPwh7OuT6gZyJZvd6WbTfrqAo7vm4xCzAXOusKqq/vWdKXphTpj5klHKNmRUU6/QRGlBsyU9mAIPaWHlqJA==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-imports@7.22.15':
    resolution: {integrity: sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-module-transforms@7.23.3':
    resolution: {integrity: sha512-7bBs4ED9OmswdfDzpz4MpWgSrV7FXlc3zIagvLFjS5H+Mk7Snr21vQ6QwrsoCGMfNC4e4LQPdoULEt4ykz0SRQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-optimise-call-expression@7.22.5':
    resolution: {integrity: sha512-HBwaojN0xFRx4yIvpwGqxiV2tUfl7401jlok564NgB9EHS1y6QT17FmKWm4ztqjeVdXLuC4fSvHc5ePpQjoTbw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-plugin-utils@7.22.5':
    resolution: {integrity: sha512-uLls06UVKgFG9QD4OeFYLEGteMIAa5kpTPcFL28yuCIIzsf6ZyKZMllKVOCZFhiZ5ptnwX4mtKdWCBE/uT4amg==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-remap-async-to-generator@7.22.20':
    resolution: {integrity: sha512-pBGyV4uBqOns+0UvhsTO8qgl8hO89PmiDYv+/COyp1aeMcmfrfruz+/nCMFiYyFF/Knn0yfrC85ZzNFjembFTw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-replace-supers@7.22.20':
    resolution: {integrity: sha512-qsW0In3dbwQUbK8kejJ4R7IHVGwHJlV6lpG6UA7a9hSa2YEiAib+N1T2kr6PEeUT+Fl7najmSOS6SmAwCHK6Tw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/helper-simple-access@7.22.5':
    resolution: {integrity: sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-skip-transparent-expression-wrappers@7.22.5':
    resolution: {integrity: sha512-tK14r66JZKiC43p8Ki33yLBVJKlQDFoA8GYN67lWCDCqoL6EMMSuM9b+Iff2jHaM/RRFYl7K+iiru7hbRqNx8Q==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-split-export-declaration@7.22.6':
    resolution: {integrity: sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-string-parser@7.23.4':
    resolution: {integrity: sha512-803gmbQdqwdf4olxrX4AJyFBV/RTr3rSmOj0rKwesmzlfhYNDEs+/iOcznzpNWlJlIlTJC2QfPFcHB6DlzdVLQ==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-identifier@7.22.20':
    resolution: {integrity: sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-validator-option@7.23.5':
    resolution: {integrity: sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==}
    engines: {node: '>=6.9.0'}

  '@babel/helper-wrap-function@7.22.20':
    resolution: {integrity: sha512-pms/UwkOpnQe/PDAEdV/d7dVCoBbB+R4FvYoHGZz+4VPcg7RtYy2KP7S2lbuWM6FCSgob5wshfGESbC/hzNXZw==}
    engines: {node: '>=6.9.0'}

  '@babel/helpers@7.23.6':
    resolution: {integrity: sha512-wCfsbN4nBidDRhpDhvcKlzHWCTlgJYUUdSJfzXb2NuBssDSIjc3xcb+znA7l+zYsFljAcGM0aFkN40cR3lXiGA==}
    engines: {node: '>=6.9.0'}

  '@babel/highlight@7.23.4':
    resolution: {integrity: sha512-acGdbYSfp2WheJoJm/EBBBLh/ID8KDc64ISZ9DYtBmC8/Q204PZJLHyzeB5qMzJ5trcOkybd78M4x2KWsUq++A==}
    engines: {node: '>=6.9.0'}

  '@babel/parser@7.23.6':
    resolution: {integrity: sha512-Z2uID7YJ7oNvAI20O9X0bblw7Qqs8Q2hFy0R9tAfnfLkp5MW0UH9eUvnDSnFwKZ0AvgS1ucqR4KzvVHgnke1VQ==}
    engines: {node: '>=6.0.0'}
    hasBin: true

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.23.3':
    resolution: {integrity: sha512-iRkKcCqb7iGnq9+3G6rZ+Ciz5VywC4XNRHe57lKM+jOeYAoR0lVqdeeDRfh0tQcTfw/+vBhHn926FmQhLtlFLQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.23.3':
    resolution: {integrity: sha512-WwlxbfMNdVEpQjZmK5mhm7oSwD3dS6eU+Iwsi4Knl9wAletWem7kaRsGOG+8UEbRyqxY4SS5zvtfXwX+jMxUwQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0

  '@babel/plugin-proposal-async-generator-functions@7.20.7':
    resolution: {integrity: sha512-xMbiLsn/8RK7Wq7VeVytytS2L6qE69bXPB10YCmMdDZbKF4okCqY74pI/jJQ/8U0b/F6NrT2+14b8/P9/3AMGA==}
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-async-generator-functions instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2':
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-proposal-unicode-property-regex@7.18.6':
    resolution: {integrity: sha512-2BShG/d5yoZyXZfVePH91urL5wTG6ASZU9M4o03lKK8u8UW1y08OMttBSOADTcJrnPMpvDXRG3G8fyLh4ovs8w==}
    engines: {node: '>=4'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-unicode-property-regex instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-async-generators@7.8.4':
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-properties@7.12.13':
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-class-static-block@7.14.5':
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-dynamic-import@7.8.3':
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-export-namespace-from@7.8.3':
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-assertions@7.23.3':
    resolution: {integrity: sha512-lPgDSU+SJLK3xmFDTV2ZRQAiM7UuUjGidwBywFavObCiZc1BeAAcMtHJKUya92hPHO+at63JJPLygilZard8jw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-attributes@7.23.3':
    resolution: {integrity: sha512-pawnE0P9g10xgoP7yKr6CK63K2FMsTE+FZidZO/1PwRdzmAPVs+HS1mAURUsgaoxammTJvULUdIkEK0gOcU2tA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-import-meta@7.10.4':
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-json-strings@7.8.3':
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4':
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3':
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-numeric-separator@7.10.4':
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-object-rest-spread@7.8.3':
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-catch-binding@7.8.3':
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-optional-chaining@7.8.3':
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-private-property-in-object@7.14.5':
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-top-level-await@7.14.5':
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6':
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-arrow-functions@7.23.3':
    resolution: {integrity: sha512-NzQcQrzaQPkaEwoTm4Mhyl8jI1huEL/WWIEvudjTCMJ9aBZNpsJbMASx7EQECtQQPS/DcnFpo0FIh3LvEO9cxQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-generator-functions@7.23.4':
    resolution: {integrity: sha512-efdkfPhHYTtn0G6n2ddrESE91fgXxjlqLsnUtPWnJs4a4mZIbUaK7ffqKIIUKXSHwcDvaCVX6GXkaJJFqtX7jw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-async-to-generator@7.22.5':
    resolution: {integrity: sha512-b1A8D8ZzE/VhNDoV1MSJTnpKkCG5bJo+19R4o4oy03zM7ws8yEMK755j61Dc3EyvdysbqH5BOOTquJ7ZX9C6vQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoped-functions@7.23.3':
    resolution: {integrity: sha512-vI+0sIaPIO6CNuM9Kk5VmXcMVRiOpDh7w2zZt9GXzmE/9KD70CUEVhvPR/etAeNK/FAEkhxQtXOzVF3EuRL41A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-block-scoping@7.23.4':
    resolution: {integrity: sha512-0QqbP6B6HOh7/8iNR4CQU2Th/bbRtBp4KS9vcaZd1fZ0wSh5Fyssg0UCIHwxh+ka+pNDREbVLQnHCMHKZfPwfw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-properties@7.23.3':
    resolution: {integrity: sha512-uM+AN8yCIjDPccsKGlw271xjJtGii+xQIF/uMPS8H15L12jZTsLfF4o5vNO7d/oUguOyfdikHGc/yi9ge4SGIg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-class-static-block@7.23.4':
    resolution: {integrity: sha512-nsWu/1M+ggti1SOALj3hfx5FXzAY06fwPJsUZD4/A5e1bWi46VUIWtD+kOX6/IdhXGsXBWllLFDSnqSCdUNydQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0

  '@babel/plugin-transform-classes@7.23.5':
    resolution: {integrity: sha512-jvOTR4nicqYC9yzOHIhXG5emiFEOpappSJAl73SDSEDcybD+Puuze8Tnpb9p9qEyYup24tq891gkaygIFvWDqg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-computed-properties@7.23.3':
    resolution: {integrity: sha512-dTj83UVTLw/+nbiHqQSFdwO9CbTtwq1DsDqm3CUEtDrZNET5rT5E6bIdTlOftDTDLMYxvxHNEYO4B9SLl8SLZw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-destructuring@7.23.3':
    resolution: {integrity: sha512-n225npDqjDIr967cMScVKHXJs7rout1q+tt50inyBCPkyZ8KxeI6d+GIbSBTT/w/9WdlWDOej3V9HE5Lgk57gw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dotall-regex@7.23.3':
    resolution: {integrity: sha512-vgnFYDHAKzFaTVp+mneDsIEbnJ2Np/9ng9iviHw3P/KVcgONxpNULEW/51Z/BaFojG2GI2GwwXck5uV1+1NOYQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-duplicate-keys@7.23.3':
    resolution: {integrity: sha512-RrqQ+BQmU3Oyav3J+7/myfvRCq7Tbz+kKLLshUmMwNlDHExbGL7ARhajvoBJEvc+fCguPPu887N+3RRXBVKZUA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-dynamic-import@7.23.4':
    resolution: {integrity: sha512-V6jIbLhdJK86MaLh4Jpghi8ho5fGzt3imHOBu/x0jlBaPYqDoWz4RDXjmMOfnh+JWNaQleEAByZLV0QzBT4YQQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-exponentiation-operator@7.23.3':
    resolution: {integrity: sha512-5fhCsl1odX96u7ILKHBj4/Y8vipoqwsJMh4csSA8qFfxrZDEA4Ssku2DyNvMJSmZNOEBT750LfFPbtrnTP90BQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-export-namespace-from@7.23.4':
    resolution: {integrity: sha512-GzuSBcKkx62dGzZI1WVgTWvkkz84FZO5TC5T8dl/Tht/rAla6Dg/Mz9Yhypg+ezVACf/rgDuQt3kbWEv7LdUDQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-for-of@7.23.6':
    resolution: {integrity: sha512-aYH4ytZ0qSuBbpfhuofbg/e96oQ7U2w1Aw/UQmKT+1l39uEhUPoFS3fHevDc1G0OvewyDudfMKY1OulczHzWIw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-function-name@7.23.3':
    resolution: {integrity: sha512-I1QXp1LxIvt8yLaib49dRW5Okt7Q4oaxao6tFVKS/anCdEOMtYwWVKoiOA1p34GOWIZjUK0E+zCp7+l1pfQyiw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-json-strings@7.23.4':
    resolution: {integrity: sha512-81nTOqM1dMwZ/aRXQ59zVubN9wHGqk6UtqRK+/q+ciXmRy8fSolhGVvG09HHRGo4l6fr/c4ZhXUQH0uFW7PZbg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-literals@7.23.3':
    resolution: {integrity: sha512-wZ0PIXRxnwZvl9AYpqNUxpZ5BiTGrYt7kueGQ+N5FiQ7RCOD4cm8iShd6S6ggfVIWaJf2EMk8eRzAh52RfP4rQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-logical-assignment-operators@7.23.4':
    resolution: {integrity: sha512-Mc/ALf1rmZTP4JKKEhUwiORU+vcfarFVLfcFiolKUo6sewoxSEgl36ak5t+4WamRsNr6nzjZXQjM35WsU+9vbg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-member-expression-literals@7.23.3':
    resolution: {integrity: sha512-sC3LdDBDi5x96LA+Ytekz2ZPk8i/Ck+DEuDbRAll5rknJ5XRTSaPKEYwomLcs1AA8wg9b3KjIQRsnApj+q51Ag==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-amd@7.23.3':
    resolution: {integrity: sha512-vJYQGxeKM4t8hYCKVBlZX/gtIY2I7mRGFNcm85sgXGMTBcoV3QdVtdpbcWEbzbfUIUZKwvgFT82mRvaQIebZzw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-commonjs@7.23.3':
    resolution: {integrity: sha512-aVS0F65LKsdNOtcz6FRCpE4OgsP2OFnW46qNxNIX9h3wuzaNcSQsJysuMwqSibC98HPrf2vCgtxKNwS0DAlgcA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-systemjs@7.23.3':
    resolution: {integrity: sha512-ZxyKGTkF9xT9YJuKQRo19ewf3pXpopuYQd8cDXqNzc3mUNbOME0RKMoZxviQk74hwzfQsEe66dE92MaZbdHKNQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-modules-umd@7.23.3':
    resolution: {integrity: sha512-zHsy9iXX2nIsCBFPud3jKn1IRPWg3Ing1qOZgeKV39m1ZgIdpJqvlWVeiHBZC6ITRG0MfskhYe9cLgntfSFPIg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-named-capturing-groups-regex@7.22.5':
    resolution: {integrity: sha512-YgLLKmS3aUBhHaxp5hi1WJTgOUb/NCuDHzGT9z9WTt3YG+CPRhJs6nprbStx6DnWM4dh6gt7SU3sZodbZ08adQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/plugin-transform-new-target@7.23.3':
    resolution: {integrity: sha512-YJ3xKqtJMAT5/TIZnpAR3I+K+WaDowYbN3xyxI8zxx/Gsypwf9B9h0VB+1Nh6ACAAPRS5NSRje0uVv5i79HYGQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-nullish-coalescing-operator@7.23.4':
    resolution: {integrity: sha512-jHE9EVVqHKAQx+VePv5LLGHjmHSJR76vawFPTdlxR/LVJPfOEGxREQwQfjuZEOPTwG92X3LINSh3M40Rv4zpVA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-numeric-separator@7.23.4':
    resolution: {integrity: sha512-mps6auzgwjRrwKEZA05cOwuDc9FAzoyFS4ZsG/8F43bTLf/TgkJg7QXOrPO1JO599iA3qgK9MXdMGOEC8O1h6Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-rest-spread@7.23.4':
    resolution: {integrity: sha512-9x9K1YyeQVw0iOXJlIzwm8ltobIIv7j2iLyP2jIhEbqPRQ7ScNgwQufU2I0Gq11VjyG4gI4yMXt2VFags+1N3g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-object-super@7.23.3':
    resolution: {integrity: sha512-BwQ8q0x2JG+3lxCVFohg+KbQM7plfpBwThdW9A6TMtWwLsbDA01Ek2Zb/AgDN39BiZsExm4qrXxjk+P1/fzGrA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-catch-binding@7.23.4':
    resolution: {integrity: sha512-XIq8t0rJPHf6Wvmbn9nFxU6ao4c7WhghTR5WyV8SrJfUFzyxhCm4nhC+iAp3HFhbAKLfYpgzhJ6t4XCtVwqO5A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-optional-chaining@7.23.4':
    resolution: {integrity: sha512-ZU8y5zWOfjM5vZ+asjgAPwDaBjJzgufjES89Rs4Lpq63O300R/kOz30WCLo6BxxX6QVEilwSlpClnG5cZaikTA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-parameters@7.23.3':
    resolution: {integrity: sha512-09lMt6UsUb3/34BbECKVbVwrT9bO6lILWln237z7sLaWnMsTi7Yc9fhX5DLpkJzAGfaReXI22wP41SZmnAA3Vw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-methods@7.23.3':
    resolution: {integrity: sha512-UzqRcRtWsDMTLrRWFvUBDwmw06tCQH9Rl1uAjfh6ijMSmGYQ+fpdB+cnqRC8EMh5tuuxSv0/TejGL+7vyj+50g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-private-property-in-object@7.23.4':
    resolution: {integrity: sha512-9G3K1YqTq3F4Vt88Djx1UZ79PDyj+yKRnUy7cZGSMe+a7jkwD259uKKuUzQlPkGam7R+8RJwh5z4xO27fA1o2A==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-property-literals@7.23.3':
    resolution: {integrity: sha512-jR3Jn3y7cZp4oEWPFAlRsSWjxKe4PZILGBSd4nis1TsC5qeSpb+nrtihJuDhNI7QHiVbUaiXa0X2RZY3/TI6Nw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-regenerator@7.23.3':
    resolution: {integrity: sha512-KP+75h0KghBMcVpuKisx3XTu9Ncut8Q8TuvGO4IhY+9D5DFEckQefOuIsB/gQ2tG71lCke4NMrtIPS8pOj18BQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-reserved-words@7.23.3':
    resolution: {integrity: sha512-QnNTazY54YqgGxwIexMZva9gqbPa15t/x9VS+0fsEFWplwVpXYZivtgl43Z1vMpc1bdPP2PP8siFeVcnFvA3Cg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-runtime@7.22.9':
    resolution: {integrity: sha512-9KjBH61AGJetCPYp/IEyLEp47SyybZb0nDRpBvmtEkm+rUIwxdlKpyNHI1TmsGkeuLclJdleQHRZ8XLBnnh8CQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-shorthand-properties@7.23.3':
    resolution: {integrity: sha512-ED2fgqZLmexWiN+YNFX26fx4gh5qHDhn1O2gvEhreLW2iI63Sqm4llRLCXALKrCnbN4Jy0VcMQZl/SAzqug/jg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-spread@7.23.3':
    resolution: {integrity: sha512-VvfVYlrlBVu+77xVTOAoxQ6mZbnIq5FM0aGBSFEcIh03qHf+zNqA4DC/3XMUozTg7bZV3e3mZQ0i13VB6v5yUg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-sticky-regex@7.23.3':
    resolution: {integrity: sha512-HZOyN9g+rtvnOU3Yh7kSxXrKbzgrm5X4GncPY1QOquu7epga5MxKHVpYu2hvQnry/H+JjckSYRb93iNfsioAGg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-template-literals@7.23.3':
    resolution: {integrity: sha512-Flok06AYNp7GV2oJPZZcP9vZdszev6vPBkHLwxwSpaIqx75wn6mUd3UFWsSsA0l8nXAKkyCmL/sR02m8RYGeHg==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-typeof-symbol@7.23.3':
    resolution: {integrity: sha512-4t15ViVnaFdrPC74be1gXBSMzXk3B4Us9lP7uLRQHTFpV5Dvt33pn+2MyyNxmN3VTTm3oTrZVMUmuw3oBnQ2oQ==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-escapes@7.23.3':
    resolution: {integrity: sha512-OMCUx/bU6ChE3r4+ZdylEqAjaQgHAgipgW8nsCfu5pGqDcFytVd91AwRvUJSBZDz0exPGgnjoqhgRYLRjFZc9Q==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-property-regex@7.23.3':
    resolution: {integrity: sha512-KcLIm+pDZkWZQAFJ9pdfmh89EwVfmNovFBcXko8szpBeF8z68kWIPeKlmSOkT9BXJxs2C0uk+5LxoxIv62MROA==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-regex@7.23.3':
    resolution: {integrity: sha512-wMHpNA4x2cIA32b/ci3AfwNgheiva2W0WUKWTK7vBHBhDKfPsc5cFGNWm69WBqpwd86u1qwZ9PWevKqm1A3yAw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/plugin-transform-unicode-sets-regex@7.23.3':
    resolution: {integrity: sha512-W7lliA/v9bNR83Qc3q1ip9CQMZ09CcHDbHfbLRDNuAhn1Mvkr1ZNF7hPmztMQvtTGVLJ9m8IZqWsTkXOml8dbw==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0

  '@babel/preset-env@7.22.9':
    resolution: {integrity: sha512-wNi5H/Emkhll/bqPjsjQorSykrlfY5OWakd6AulLvMEytpKasMVUpVy8RL4qBIBs5Ac6/5i0/Rv0b/Fg6Eag/g==}
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0

  '@babel/preset-modules@0.1.6':
    resolution: {integrity: sha512-ID2yj6K/4lKfhuU3+EX4UvNbIt7eACFbHmNUjzA+ep+B5971CknnA/9DEWKbRokfbbtblxxxXFJJrH47UEAMVg==}
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0

  '@babel/regjsgen@0.8.0':
    resolution: {integrity: sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==}

  '@babel/runtime@7.22.6':
    resolution: {integrity: sha512-wDb5pWm4WDdF6LFUde3Jl8WzPA+3ZbxYqkC6xAXuD3irdEHN1k0NfTRrJD8ZD378SJ61miMLCqIOXYhd8x+AJQ==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.22.15':
    resolution: {integrity: sha512-QPErUVm4uyJa60rkI73qneDacvdvzxshT3kksGqlGWYdOTIUOwJ7RDUL8sGqslY1uXWSL6xMFKEXDS3ox2uF0w==}
    engines: {node: '>=6.9.0'}

  '@babel/template@7.22.5':
    resolution: {integrity: sha512-X7yV7eiwAxdj9k94NEylvbVHLiVG1nvzCV2EAowhxLTwODV1jl9UzZ48leOC0sH7OnuHrIkllaBgneUykIcZaw==}
    engines: {node: '>=6.9.0'}

  '@babel/traverse@7.23.6':
    resolution: {integrity: sha512-czastdK1e8YByZqezMPFiZ8ahwVMh/ESl9vPgvgdB9AmFMGP5jfpFax74AQgl5zj4XHzqeYAg2l8PuUeRS1MgQ==}
    engines: {node: '>=6.9.0'}

  '@babel/types@7.23.6':
    resolution: {integrity: sha512-+uarb83brBzPKN38NX1MkB6vb6+mwvR6amUulqAE7ccQw1pEl+bCia9TbdG1lsnFP7lZySvUn37CHyXQdfTwzg==}
    engines: {node: '>=6.9.0'}

  '@colors/colors@1.5.0':
    resolution: {integrity: sha512-ooWCrlZP11i8GImSjTHYHLkvFDP48nS4+204nGb1RiX/WXYHmJA2III9/e2DWVabCESdW7hBAEzHRqUn9OUVvQ==}
    engines: {node: '>=0.1.90'}

  '@discoveryjs/json-ext@0.5.7':
    resolution: {integrity: sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==}
    engines: {node: '>=10.0.0'}

  '@esbuild/android-arm64@0.18.17':
    resolution: {integrity: sha512-9np+YYdNDed5+Jgr1TdWBsozZ85U1Oa3xW0c7TWqH0y2aGghXtZsuT8nYRbzOMcl0bXZXjOGbksoTtVOlWrRZg==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [android]

  '@esbuild/android-arm@0.18.17':
    resolution: {integrity: sha512-wHsmJG/dnL3OkpAcwbgoBTTMHVi4Uyou3F5mf58ZtmUyIKfcdA7TROav/6tCzET4A3QW2Q2FC+eFneMU+iyOxg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [android]

  '@esbuild/android-x64@0.18.17':
    resolution: {integrity: sha512-O+FeWB/+xya0aLg23hHEM2E3hbfwZzjqumKMSIqcHbNvDa+dza2D0yLuymRBQQnC34CWrsJUXyH2MG5VnLd6uw==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [android]

  '@esbuild/darwin-arm64@0.18.17':
    resolution: {integrity: sha512-M9uJ9VSB1oli2BE/dJs3zVr9kcCBBsE883prage1NWz6pBS++1oNn/7soPNS3+1DGj0FrkSvnED4Bmlu1VAE9g==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [darwin]

  '@esbuild/darwin-x64@0.18.17':
    resolution: {integrity: sha512-XDre+J5YeIJDMfp3n0279DFNrGCXlxOuGsWIkRb1NThMZ0BsrWXoTg23Jer7fEXQ9Ye5QjrvXpxnhzl3bHtk0g==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [darwin]

  '@esbuild/freebsd-arm64@0.18.17':
    resolution: {integrity: sha512-cjTzGa3QlNfERa0+ptykyxs5A6FEUQQF0MuilYXYBGdBxD3vxJcKnzDlhDCa1VAJCmAxed6mYhA2KaJIbtiNuQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [freebsd]

  '@esbuild/freebsd-x64@0.18.17':
    resolution: {integrity: sha512-sOxEvR8d7V7Kw8QqzxWc7bFfnWnGdaFBut1dRUYtu+EIRXefBc/eIsiUiShnW0hM3FmQ5Zf27suDuHsKgZ5QrA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [freebsd]

  '@esbuild/linux-arm64@0.18.17':
    resolution: {integrity: sha512-c9w3tE7qA3CYWjT+M3BMbwMt+0JYOp3vCMKgVBrCl1nwjAlOMYzEo+gG7QaZ9AtqZFj5MbUc885wuBBmu6aADQ==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [linux]

  '@esbuild/linux-arm@0.18.17':
    resolution: {integrity: sha512-2d3Lw6wkwgSLC2fIvXKoMNGVaeY8qdN0IC3rfuVxJp89CRfA3e3VqWifGDfuakPmp90+ZirmTfye1n4ncjv2lg==}
    engines: {node: '>=12'}
    cpu: [arm]
    os: [linux]

  '@esbuild/linux-ia32@0.18.17':
    resolution: {integrity: sha512-1DS9F966pn5pPnqXYz16dQqWIB0dmDfAQZd6jSSpiT9eX1NzKh07J6VKR3AoXXXEk6CqZMojiVDSZi1SlmKVdg==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [linux]

  '@esbuild/linux-loong64@0.18.17':
    resolution: {integrity: sha512-EvLsxCk6ZF0fpCB6w6eOI2Fc8KW5N6sHlIovNe8uOFObL2O+Mr0bflPHyHwLT6rwMg9r77WOAWb2FqCQrVnwFg==}
    engines: {node: '>=12'}
    cpu: [loong64]
    os: [linux]

  '@esbuild/linux-mips64el@0.18.17':
    resolution: {integrity: sha512-e0bIdHA5p6l+lwqTE36NAW5hHtw2tNRmHlGBygZC14QObsA3bD4C6sXLJjvnDIjSKhW1/0S3eDy+QmX/uZWEYQ==}
    engines: {node: '>=12'}
    cpu: [mips64el]
    os: [linux]

  '@esbuild/linux-ppc64@0.18.17':
    resolution: {integrity: sha512-BAAilJ0M5O2uMxHYGjFKn4nJKF6fNCdP1E0o5t5fvMYYzeIqy2JdAP88Az5LHt9qBoUa4tDaRpfWt21ep5/WqQ==}
    engines: {node: '>=12'}
    cpu: [ppc64]
    os: [linux]

  '@esbuild/linux-riscv64@0.18.17':
    resolution: {integrity: sha512-Wh/HW2MPnC3b8BqRSIme/9Zhab36PPH+3zam5pqGRH4pE+4xTrVLx2+XdGp6fVS3L2x+DrsIcsbMleex8fbE6g==}
    engines: {node: '>=12'}
    cpu: [riscv64]
    os: [linux]

  '@esbuild/linux-s390x@0.18.17':
    resolution: {integrity: sha512-j/34jAl3ul3PNcK3pfI0NSlBANduT2UO5kZ7FCaK33XFv3chDhICLY8wJJWIhiQ+YNdQ9dxqQctRg2bvrMlYgg==}
    engines: {node: '>=12'}
    cpu: [s390x]
    os: [linux]

  '@esbuild/linux-x64@0.18.17':
    resolution: {integrity: sha512-QM50vJ/y+8I60qEmFxMoxIx4de03pGo2HwxdBeFd4nMh364X6TIBZ6VQ5UQmPbQWUVWHWws5MmJXlHAXvJEmpQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [linux]

  '@esbuild/netbsd-x64@0.18.17':
    resolution: {integrity: sha512-/jGlhWR7Sj9JPZHzXyyMZ1RFMkNPjC6QIAan0sDOtIo2TYk3tZn5UDrkE0XgsTQCxWTTOcMPf9p6Rh2hXtl5TQ==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [netbsd]

  '@esbuild/openbsd-x64@0.18.17':
    resolution: {integrity: sha512-rSEeYaGgyGGf4qZM2NonMhMOP/5EHp4u9ehFiBrg7stH6BYEEjlkVREuDEcQ0LfIl53OXLxNbfuIj7mr5m29TA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [openbsd]

  '@esbuild/sunos-x64@0.18.17':
    resolution: {integrity: sha512-Y7ZBbkLqlSgn4+zot4KUNYst0bFoO68tRgI6mY2FIM+b7ZbyNVtNbDP5y8qlu4/knZZ73fgJDlXID+ohY5zt5g==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [sunos]

  '@esbuild/win32-arm64@0.18.17':
    resolution: {integrity: sha512-bwPmTJsEQcbZk26oYpc4c/8PvTY3J5/QK8jM19DVlEsAB41M39aWovWoHtNm78sd6ip6prilxeHosPADXtEJFw==}
    engines: {node: '>=12'}
    cpu: [arm64]
    os: [win32]

  '@esbuild/win32-ia32@0.18.17':
    resolution: {integrity: sha512-H/XaPtPKli2MhW+3CQueo6Ni3Avggi6hP/YvgkEe1aSaxw+AeO8MFjq8DlgfTd9Iz4Yih3QCZI6YLMoyccnPRg==}
    engines: {node: '>=12'}
    cpu: [ia32]
    os: [win32]

  '@esbuild/win32-x64@0.18.17':
    resolution: {integrity: sha512-fGEb8f2BSA3CW7riJVurug65ACLuQAzKq0SSqkY2b2yHHH0MzDfbLyKIGzHwOI/gkHcxM/leuSW6D5w/LMNitA==}
    engines: {node: '>=12'}
    cpu: [x64]
    os: [win32]

  '@gar/promisify@1.1.3':
    resolution: {integrity: sha512-k2Ty1JcVojjJFwrg/ThKi2ujJ7XNLYaFGNB/bWT9wGR+oSMJHMa5w+CUq6p/pVrKeNNgA7pCqEcjSnHVoqJQFw==}

  '@isaacs/cliui@8.0.2':
    resolution: {integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==}
    engines: {node: '>=12'}

  '@istanbuljs/load-nyc-config@1.1.0':
    resolution: {integrity: sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==}
    engines: {node: '>=8'}

  '@istanbuljs/schema@0.1.3':
    resolution: {integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==}
    engines: {node: '>=8'}

  '@jridgewell/gen-mapping@0.3.3':
    resolution: {integrity: sha512-HLhSWOLRi875zjjMG/r+Nv0oCW8umGb0BgEhyX3dDX3egwZtB8PqLnjz3yedt8R5StBrzcg4aBpnh8UA9D1BoQ==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/resolve-uri@3.1.1':
    resolution: {integrity: sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/set-array@1.1.2':
    resolution: {integrity: sha512-xnkseuNADM0gt2bs+BvhO0p78Mk762YnZdsuzFV018NoG1Sj1SCQvpSqa7XUaTam5vAGasABV9qXASMKnFMwMw==}
    engines: {node: '>=6.0.0'}

  '@jridgewell/source-map@0.3.5':
    resolution: {integrity: sha512-UTYAUj/wviwdsMfzoSJspJxbkH5o1snzwX0//0ENX1u/55kkZZkcTZP6u9bwKGkv+dkk9at4m1Cpt0uY80kcpQ==}

  '@jridgewell/sourcemap-codec@1.4.15':
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==}

  '@jridgewell/trace-mapping@0.3.20':
    resolution: {integrity: sha512-R8LcPeWZol2zR8mmH3JeKQ6QRCFb7XgUhV9ZlGhHLGyg4wpPiPZNQOOWhFZhxKw8u//yTbNGI42Bx/3paXEQ+Q==}

  '@leichtgewicht/ip-codec@2.0.4':
    resolution: {integrity: sha512-Hcv+nVC0kZnQ3tD9GVu5xSMR4VVYOteQIr/hwFPVEvPdlXqgGEuRjiheChHgdM+JyqdgNcmzZOX/tnl0JOiI7A==}

  '@material/animation@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-leRf+BcZTfC/iSigLXnYgcHAGvFVQveoJT5+2PIRdyPI/bIG7hhciRgacHRsCKC0sGya81dDblLgdkjSUemYLw==}

  '@material/auto-init@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-uxzDq7q3c0Bu1pAsMugc1Ik9ftQYQqZY+5e2ybNplT8gTImJhNt4M2mMiMHbMANk2l3UgICmUyRSomgPBWCPIA==}

  '@material/banner@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-SHeVoidCUFVhXANN6MNWxK9SZoTSgpIP8GZB7kAl52BywLxtV+FirTtLXkg/8RUkxZRyRWl7HvQ0ZFZa7QQAyA==}

  '@material/base@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-Fc3vGuOf+duGo22HTRP6dHdc+MUe0VqQfWOuKrn/wXKD62m0QQR2TqJd3rRhCumH557T5QUyheW943M3E+IGfg==}

  '@material/button@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-3AQgwrPZCTWHDJvwgKq7Cj+BurQ4wTjDdGL+FEnIGUAjJDskwi1yzx5tW2Wf/NxIi7IoPFyOY3UB41jwMiOrnw==}

  '@material/card@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-nPlhiWvbLmooTnBmV5gmzB0eLWSgLKsSRBYAbIBmO76Okgz1y+fQNLag+lpm/TDaHVsn5fmQJH8e0zIg0rYsQA==}

  '@material/checkbox@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-4tpNnO1L0IppoMF3oeQn8F17t2n0WHB0D7mdJK9rhrujen/fLbekkIC82APB3fdGtLGg3qeNqDqPsJm1YnmrwA==}

  '@material/chips@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-fqHKvE5bSWK0bXVkf57MWxZtytGqYBZvvHIOs4JI9HPHEhaJy4CpSw562BEtbm3yFxxALoQknvPW2KYzvADnmA==}

  '@material/circular-progress@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-Lxe8BGAxQwCQqrLhrYrIP0Uok10h7aYS3RBXP41ph+5GmwJd5zdyE2t93qm2dyThvU6qKuXw9726Dtq/N+wvZQ==}

  '@material/data-table@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-j/7qplT9+sUpfe4pyWhPbl01qJA+OoNAG3VMJruBBR461ZBKyTi7ssKH9yksFGZ8eCEPkOsk/+kDxsiZvRWkeQ==}

  '@material/density@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-Zt3u07fXrBWLW06Tl5fgvjicxNQMkFdawLyNTzZ5TvbXfVkErILLePwwGaw8LNcvzqJP6ABLA8jiR+sKNoJQCg==}

  '@material/dialog@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-o+9a/fmwJ9+gY3Z/uhj/PMVJDq7it1NTWKJn2GwAKdB+fDkT4hb9qEdcxMPyvJJ5ups+XiKZo03+tZrD+38c1w==}

  '@material/dom@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-ly78R7aoCJtundSUu0UROU+5pQD5Piae0Y1MkN6bs0724azeazX1KeXFeaf06JOXnlr5/41ol+fSUPowjoqnOg==}

  '@material/drawer@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-PFL4cEFnt7VTxDsuspFVNhsFDYyumjU0VWfj3PWB7XudsEfQ3lo85D3HCEtTTbRsCainGN8bgYNDNafLBqiigw==}

  '@material/elevation@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-Ro+Pk8jFuap+T0B0shA3xI1hs2b89dNQ2EIPCNjNMp87emHKAzJfhKb7EZGIwv3+gFLlVaLyIVkb94I89KLsyg==}

  '@material/fab@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-dvU0KWMRglwJEQwmQtFAmJcAjzg9VFF6Aqj78bJYu/DAIGFJ1VTTTSgoXM/XCm1YyQEZ7kZRvxBO37CH54rSDg==}

  '@material/feature-targeting@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-wkDjVcoVEYYaJvun28IXdln/foLgPD7n9ZC9TY76GErGCwTq+HWpU6wBAAk+ePmpRFDayw4vI4wBlaWGxLtysQ==}

  '@material/floating-label@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-bUWPtXzZITOD/2mkvLkEPO1ngDWmb74y0Kgbz6llHLOQBtycyJIpuoQJ1q2Ez0NM/tFLwPphhAgRqmL3YQ/Kzw==}

  '@material/focus-ring@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-cZHThVose3GvAlJzpJoBI1iqL6d1/Jj9hXrR+r8Mwtb1hBIUEG3hxfsRd4vGREuzROPlf0OgNf/V+YHoSwgR5w==}

  '@material/form-field@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-+JFXy5X44Gue1CbZZAQ6YejnI203lebYwL0i6k0ylDpWHEOdD5xkF2PyHR28r9/65Ebcbwbff6q7kI1SGoT7MA==}

  '@material/icon-button@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-1a0MHgyIwOs4RzxrVljsqSizGYFlM1zY2AZaLDsgT4G3kzsplTx8HZQ022GpUCjAygW+WLvg4z1qAhQHvsbqlw==}

  '@material/image-list@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-WKWmiYap2iu4QdqmeUSliLlN4O2Ueqa0OuVAYHn/TCzmQ2xmnhZ1pvDLbs6TplpOmlki7vFfe+aSt5SU9gwfOQ==}

  '@material/layout-grid@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-5GqmT6oTZhUGWIb+CLD0ZNyDyTiJsr/rm9oRIi3+vCujACwxFkON9tzBlZohdtFS16nuzUusthN6Jt9UrJcN6Q==}

  '@material/line-ripple@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-8S30WXEuUdgDdBulzUDlPXD6qMzwCX9SxYb5mGDYLwl199cpSGdXHtGgEcCjokvnpLhdZhcT1Dsxeo1g2Evh5Q==}

  '@material/linear-progress@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-6EJpjrz6aoH2/gXLg9iMe0yF2C42hpQyZoHpmcgTLKeci85ktDvJIjwup8tnk8ULQyFiGiIrhXw2v2RSsiFjvQ==}

  '@material/list@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-TQ1ppqiCMQj/P7bGD4edbIIv4goczZUoiUAaPq/feb1dflvrFMzYqJ7tQRRCyBL8nRhJoI2x99tk8Q2RXvlGUQ==}

  '@material/menu-surface@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-dMtSPN+olTWE+08M5qe4ea1IZOhVryYqzK0Gyb2u1G75rSArUxCOB5rr6OC/ST3Mq3RS6zGuYo7srZt4534K9Q==}

  '@material/menu@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-IlAh61xzrzxXs38QZlt74UYt8J431zGznSzDtB1Fqs6YFNd11QPKoiRXn1J2Qu/lUxbFV7i8NBKMCKtia0n6/Q==}

  '@material/notched-outline@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-WuurMg44xexkvLTBTnsO0A+qnzFjpcPdvgWBGstBepYozsvSF9zJGdb1x7Zv1MmqbpYh/Ohnuxtb/Y3jOh6irg==}

  '@material/progress-indicator@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-uOnsvqw5F2fkeTnTl4MrYzjI7KCLmmLyZaM0cgLNuLsWVlddQE+SGMl28tENx7DUK3HebWq0FxCP8f25LuDD+w==}

  '@material/radio@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-ehzOK+U1IxQN+OQjgD2lsnf1t7t7RAwQzeO6Czkiuid29ookYbQynWuLWk7NW8H8ohl7lnmfqTP1xSNkkL/F0g==}

  '@material/ripple@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-JfLW+g3GMVDv4cruQ19+HUxpKVdWCldFlIPw1UYezz2h3WTNDy05S3uP2zUdXzZ01C3dkBFviv4nqZ0GCT16MA==}

  '@material/rtl@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-SkKLNLFp5QtG7/JEFg9R92qq4MzTcZ5As6sWbH7rRg6ahTHoJEuqE+pOb9Vrtbj84k5gtX+vCYPvCILtSlr2uw==}

  '@material/segmented-button@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-YDwkCWP9l5mIZJ7pZJZ2hMDxfBlIGVJ+deNzr8O+Z7/xC5LGXbl4R5aPtUVHygvXAXxpf5096ZD+dSXzYzvWlw==}

  '@material/select@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-unfOWVf7T0sixVG+3k3RTuATfzqvCF6QAzA6J9rlCh/Tq4HuIBNDdV4z19IVu4zwmgWYxY0iSvqWUvdJJYwakQ==}

  '@material/shape@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-Dsvr771ZKC46ODzoixLdGwlLEQLfxfLrtnRojXABoZf5G3o9KtJU+J+5Ld5aa960OAsCzzANuaub4iR88b1guA==}

  '@material/slider@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-3AEu+7PwW4DSNLndue47dh2u7ga4hDJRYmuu7wnJCIWJBnLCkp6C92kNc4Rj5iQY2ftJio5aj1gqryluh5tlYg==}

  '@material/snackbar@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-TwwQSYxfGK6mc03/rdDamycND6o+1p61WNd7ElZv1F1CLxB4ihRjbCoH7Qo+oVDaP8CTpjeclka+24RLhQq0mA==}

  '@material/switch@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-OjUjtT0kRz1ASAsOS+dNzwMwvsjmqy5edK57692qmrP6bL4GblFfBDoiNJ6t0AN4OaKcmL5Hy/xNrTdOZW7Qqw==}

  '@material/tab-bar@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-Xmtq0wJGfu5k+zQeFeNsr4bUKv7L+feCmUp/gsapJ655LQKMXOUQZtSv9ZqWOfrCMy55hoF1CzGFV+oN3tyWWQ==}

  '@material/tab-indicator@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-despCJYi1GrDDq7F2hvLQkObHnSLZPPDxnOzU16zJ6FNYvIdszgfzn2HgAZ6pl5hLOexQ8cla6cAqjTDuaJBhQ==}

  '@material/tab-scroller@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-QWHG/EWxirj4V9u2IHz+OSY9XCWrnNrPnNgEufxAJVUKV/A8ma1DYeFSQqxhX709R8wKGdycJksg0Flkl7Gq7w==}

  '@material/tab@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-s/L9otAwn/pZwVQZBRQJmPqYeNbjoEbzbjMpDQf/VBG/6dJ+aP03ilIBEkqo8NVnCoChqcdtVCoDNRtbU+yp6w==}

  '@material/textfield@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-R3qRex9kCaZIAK8DuxPnVC42R0OaW7AB7fsFknDKeTeVQvRcbnV8E+iWSdqTiGdsi6QQHifX8idUrXw+O45zPw==}

  '@material/theme@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-CpUwXGE0dbhxQ45Hu9r9wbJtO/MAlv5ER4tBHA9tp/K+SU+lDgurBE2touFMg5INmdfVNtdumxb0nPPLaNQcUg==}

  '@material/tokens@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-nbEuGj05txWz6ZMUanpM47SaAD7soyjKILR+XwDell9Zg3bGhsnexCNXPEz2fD+YgomS+jM5XmIcaJJHg/H93Q==}

  '@material/tooltip@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-UzuXp0b9NuWuYLYpPguxrjbJnCmT/Cco8CkjI/6JajxaeA3o2XEBbQfRMTq8PTafuBjCHTc0b0mQY7rtxUp1Gg==}

  '@material/top-app-bar@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-vJWjsvqtdSD5+yQ/9vgoBtBSCvPJ5uF/DVssv8Hdhgs1PYaAcODUi77kdi0+sy/TaWyOsTkQixqmwnFS16zesA==}

  '@material/touch-target@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-AqYh9fjt+tv4ZE0C6MeYHblS2H+XwLbDl2mtyrK0DOEnCVQk5/l5ImKDfhrUdFWHvS4a5nBM4AA+sa7KaroLoA==}

  '@material/typography@15.0.0-canary.bc9ae6c9c.0':
    resolution: {integrity: sha512-CKsG1zyv34AKPNyZC8olER2OdPII64iR2SzQjpqh1UUvmIFiMPk23LvQ1OnC5aCB14pOXzmVgvJt31r9eNdZ6Q==}

  '@mattlewis92/dom-autoscroller@2.4.2':
    resolution: {integrity: sha512-YbrUWREPGEjE/FU6foXcAT1YbVwqD/jkYnY1dFb0o4AxtP3s4xKBthlELjndZih8uwsDWgQZx1eNskRNe2BgZQ==}

  '@ngtools/webpack@16.2.14':
    resolution: {integrity: sha512-3+zPP3Wir46qrZ3FEiTz5/emSoVHYUCH+WgBmJ57mZCx1qBOYh2VgllnPr/Yusl1sc/jUZjdwq/es/9ZNw+zDQ==}
    engines: {node: ^16.14.0 || >=18.10.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}
    peerDependencies:
      '@angular/compiler-cli': ^16.0.0
      typescript: '>=4.9.3 <5.2'
      webpack: ^5.54.0

  '@nguniversal/builders@16.2.0':
    resolution: {integrity: sha512-RCthtWvPy2u3DYM6T+z06VzMKbNkucXX/hPHMPpaY5oMgTe/5k7vtOSNogldNMantgMrKV70GYmtWb2QbD4Zbg==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular-devkit/build-angular': ^16.0.0 || ^16.1.0-next.0

  '@nguniversal/common@16.2.0':
    resolution: {integrity: sha512-b3dQAwD2iI2kzF3O1mhwh6bhDg1SlT46K9lOSzNZNXy0bvV4WrSpVmfN7YKINZLFal5uwHn4j1LNdrDR4Qohlw==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular/common': ^16.0.0 || ^16.1.0-next.0
      '@angular/core': ^16.0.0 || ^16.1.0-next.0

  '@nguniversal/express-engine@16.2.0':
    resolution: {integrity: sha512-Pg77DfDvKtLzRUTahjv3WxWUldpNQTHIlyoU0wYrwoi/7qnHzoXdrvI1tqzC+DqPwxOmkGySoiRa2jJ+9IgvKw==}
    engines: {node: ^16.14.0 || >=18.10.0}
    peerDependencies:
      '@angular/common': ^16.0.0 || ^16.1.0-next.0
      '@angular/core': ^16.0.0 || ^16.1.0-next.0
      express: ^4.15.2

  '@nodelib/fs.scandir@2.1.5':
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==}
    engines: {node: '>= 8'}

  '@nodelib/fs.stat@2.0.5':
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==}
    engines: {node: '>= 8'}

  '@nodelib/fs.walk@1.2.8':
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==}
    engines: {node: '>= 8'}

  '@npmcli/fs@2.1.2':
    resolution: {integrity: sha512-yOJKRvohFOaLqipNtwYB9WugyZKhC/DZC4VYPmpaCzDBrA8YpK3qHZ8/HGscMnE4GqbkLNuVcCnxkeQEdGt6LQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  '@npmcli/fs@3.1.0':
    resolution: {integrity: sha512-7kZUAaLscfgbwBQRbvdMYaZOWyMEcPTH/tJjnyAWJ/dvvs9Ef+CERx/qJb9GExJpl1qipaDGn7KqHnFGGixd0w==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/git@4.1.0':
    resolution: {integrity: sha512-9hwoB3gStVfa0N31ymBmrX+GuDGdVA/QWShZVqE0HK2Af+7QGGrCTbZia/SW0ImUTjTne7SP91qxDmtXvDHRPQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/installed-package-contents@2.0.2':
    resolution: {integrity: sha512-xACzLPhnfD51GKvTOOuNX2/V4G4mz9/1I2MfDoye9kBM3RYe5g2YbscsaGoTlaWqkxeiapBWyseULVKpSVHtKQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    hasBin: true

  '@npmcli/move-file@2.0.1':
    resolution: {integrity: sha512-mJd2Z5TjYWq/ttPLLGqArdtnC74J6bOzg4rMDnN+p1xTacZ2yPRCk2y0oSWQtygLR9YVQXgOcONrwtnk3JupxQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This functionality has been moved to @npmcli/fs

  '@npmcli/node-gyp@3.0.0':
    resolution: {integrity: sha512-gp8pRXC2oOxu0DUE1/M3bYtb1b3/DbJ5aM113+XJBgfXdussRAsX0YOrOhdd8WvnAR6auDBvJomGAkLKA5ydxA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/promise-spawn@6.0.2':
    resolution: {integrity: sha512-gGq0NJkIGSwdbUt4yhdF8ZrmkGKVz9vAdVzpOfnom+V8PLSmSOVhZwbNvZZS1EYcJN5hzzKBxmmVVAInM6HQLg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@npmcli/run-script@6.0.2':
    resolution: {integrity: sha512-NCcr1uQo1k5U+SYlnIrbAh3cxy+OQT1VtqiAbxdymSlptbzBb62AjH2xXgjNCoP073hoa1CfCAcwoZ8k96C4nA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@pkgjs/parseargs@0.11.0':
    resolution: {integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==}
    engines: {node: '>=14'}

  '@scarf/scarf@1.4.0':
    resolution: {integrity: sha512-xxeapPiUXdZAE3che6f3xogoJPeZgig6omHEy1rIY5WVsB3H2BHNnZH+gHG6x91SCWyQCzWGsuL2Hh3ClO5/qQ==}

  '@schematics/angular@16.2.14':
    resolution: {integrity: sha512-YqIv727l9Qze8/OL6H9mBHc2jVXzAGRNBYnxYWqWhLbfvuVbbldo6NNIIjgv6lrl2LJSdPAAMNOD5m/f6210ug==}
    engines: {node: ^16.14.0 || >=18.10.0, npm: ^6.11.0 || ^7.5.6 || >=8.0.0, yarn: '>= 1.13.0'}

  '@sigstore/bundle@1.1.0':
    resolution: {integrity: sha512-PFutXEy0SmQxYI4texPw3dd2KewuNqv7OuK1ZFtY2fM754yhvG2KdgwIhRnoEE2uHdtdGNQ8s0lb94dW9sELog==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@sigstore/protobuf-specs@0.2.1':
    resolution: {integrity: sha512-XTWVxnWJu+c1oCshMLwnKvz8ZQJJDVOlciMfgpJBQbThVjKTCG8dwyhgLngBD2KN0ap9F/gOV8rFDEx8uh7R2A==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@sigstore/sign@1.0.0':
    resolution: {integrity: sha512-INxFVNQteLtcfGmcoldzV6Je0sbbfh9I16DM4yJPw3j5+TFP8X6uIiA18mvpEa9yyeycAKgPmOA3X9hVdVTPUA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@sigstore/tuf@1.0.3':
    resolution: {integrity: sha512-2bRovzs0nJZFlCN3rXirE4gwxCn97JNjMmwpecqlbgV9WcxX7WRuIrgzx/X7Ib7MYRbyUTpBYE0s2x6AmZXnlg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@socket.io/component-emitter@3.1.0':
    resolution: {integrity: sha512-+9jVqKhRSpsc591z5vX+X5Yyw+he/HCB4iQ/RYxw35CEPaY1gnsNE43nf9n9AaYjAQrTiI/mOwKUKdUs9vf7Xg==}

  '@tootallnate/once@1.1.2':
    resolution: {integrity: sha512-RbzJvlNzmRq5c3O09UipeuXno4tA1FE6ikOjxZK0tuxVv3412l64l5t1W5pj4+rJq9vpkm/kwiR07aZXnsKPxw==}
    engines: {node: '>= 6'}

  '@tootallnate/once@2.0.0':
    resolution: {integrity: sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==}
    engines: {node: '>= 10'}

  '@tufjs/canonical-json@1.0.0':
    resolution: {integrity: sha512-QTnf++uxunWvG2z3UFNzAoQPHxnSXOwtaI3iJ+AohhV+5vONuArPjJE7aPXPVXfXJsqrVbZBu9b81AJoSd09IQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@tufjs/models@1.0.4':
    resolution: {integrity: sha512-qaGV9ltJP0EO25YfFUPhxRVK0evXFIAGicsVXuRim4Ed9cjPxYhNnNJ49SFmbeLgtxpslIkX317IgpfcHPVj/A==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  '@types/body-parser@1.19.5':
    resolution: {integrity: sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==}

  '@types/bonjour@3.5.13':
    resolution: {integrity: sha512-z9fJ5Im06zvUL548KvYNecEVlA7cVDkGUi6kZusb04mpyEFKCIZJvloCcmpmLaIahDpOQGHaHmG6imtPMmPXGQ==}

  '@types/connect-history-api-fallback@1.5.4':
    resolution: {integrity: sha512-n6Cr2xS1h4uAulPRdlw6Jl6s1oG8KrVilPN2yUITEs+K48EzMJJ3W1xy8K5eWuFvjp3R74AOIGSmp2UfBJ8HFw==}

  '@types/connect@3.4.38':
    resolution: {integrity: sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==}

  '@types/cookie@0.4.1':
    resolution: {integrity: sha512-XW/Aa8APYr6jSVVA1y/DEIZX0/GMKLEVekNG727R8cs56ahETkRAy/3DR7+fJyh7oUgGwNQaRfXCun0+KbWY7Q==}

  '@types/cors@2.8.17':
    resolution: {integrity: sha512-8CGDvrBj1zgo2qE+oS3pOCyYNqCPryMWY2bGfwA0dcfopWGgxs+78df0Rs3rc9THP4JkOhLsAa+15VdpAqkcUA==}

  '@types/eslint-scope@3.7.7':
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==}

  '@types/eslint@8.56.0':
    resolution: {integrity: sha512-FlsN0p4FhuYRjIxpbdXovvHQhtlG05O1GG/RNWvdAxTboR438IOTwmrY/vLA+Xfgg06BTkP045M3vpFwTMv1dg==}

  '@types/estree@1.0.5':
    resolution: {integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==}

  '@types/express-serve-static-core@4.17.41':
    resolution: {integrity: sha512-OaJ7XLaelTgrvlZD8/aa0vvvxZdUmlCn6MtWeB7TkiKW70BQLc9XEPpDLPdbo52ZhXUCrznlWdCHWxJWtdyajA==}

  '@types/express@4.17.21':
    resolution: {integrity: sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==}

  '@types/http-errors@2.0.4':
    resolution: {integrity: sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==}

  '@types/http-proxy@1.17.14':
    resolution: {integrity: sha512-SSrD0c1OQzlFX7pGu1eXxSEjemej64aaNPRhhVYUGqXh0BtldAAx37MG8btcumvpgKyZp1F5Gn3JkktdxiFv6w==}

  '@types/jasmine@4.3.6':
    resolution: {integrity: sha512-3N0FpQTeiWjm+Oo1WUYWguUS7E6JLceiGTriFrG8k5PU7zRLJCzLcWURU3wjMbZGS//a2/LgjsnO3QxIlwxt9g==}

  '@types/json-schema@7.0.15':
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==}

  '@types/mime@1.3.5':
    resolution: {integrity: sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==}

  '@types/mime@3.0.4':
    resolution: {integrity: sha512-iJt33IQnVRkqeqC7PzBHPTC6fDlRNRW8vjrgqtScAhrmMwe8c4Eo7+fUGTa+XdWrpEgpyKWMYmi2dIwMAYRzPw==}

  '@types/node-forge@1.3.10':
    resolution: {integrity: sha512-y6PJDYN4xYBxwd22l+OVH35N+1fCYWiuC3aiP2SlXVE6Lo7SS+rSx9r89hLxrP4pn6n1lBGhHJ12pj3F3Mpttw==}

  '@types/node@16.18.97':
    resolution: {integrity: sha512-4muilE1Lbfn57unR+/nT9AFjWk0MtWi5muwCEJqnOvfRQDbSfLCUdN7vCIg8TYuaANfhLOV85ve+FNpiUsbSRg==}

  '@types/qs@6.9.11':
    resolution: {integrity: sha512-oGk0gmhnEJK4Yyk+oI7EfXsLayXatCWPHary1MtcmbAifkobT9cM9yutG/hZKIseOU0MqbIwQ/u2nn/Gb+ltuQ==}

  '@types/range-parser@1.2.7':
    resolution: {integrity: sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==}

  '@types/retry@0.12.0':
    resolution: {integrity: sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==}

  '@types/send@0.17.4':
    resolution: {integrity: sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==}

  '@types/serve-index@1.9.4':
    resolution: {integrity: sha512-qLpGZ/c2fhSs5gnYsQxtDEq3Oy8SXPClIXkW5ghvAvsNuVSA8k+gCONcUCS/UjLEYvYps+e8uBtfgXgvhwfNug==}

  '@types/serve-static@1.15.5':
    resolution: {integrity: sha512-PDRk21MnK70hja/YF8AHfC7yIsiQHn1rcXx7ijCFBX/k+XQJhQT/gw3xekXKJvx+5SXaMMS8oqQy09Mzvz2TuQ==}

  '@types/sockjs@0.3.36':
    resolution: {integrity: sha512-MK9V6NzAS1+Ud7JV9lJLFqW85VbC9dq3LmwZCuBe4wBDgKC0Kj/jd8Xl+nSviU+Qc3+m7umHHyHg//2KSa0a0Q==}

  '@types/ws@8.5.10':
    resolution: {integrity: sha512-vmQSUcfalpIq0R9q7uTo2lXs6eGIpt9wtnLdMv9LVpIjCA/+ufZRozlVoVelIYixx1ugCBKDhn89vnsEGOCx9A==}

  '@vitejs/plugin-basic-ssl@1.0.1':
    resolution: {integrity: sha512-pcub+YbFtFhaGRTo1832FQHQSHvMrlb43974e2eS8EKleR3p1cDdkJFPci1UhwkEf1J9Bz+wKBSzqpKp7nNj2A==}
    engines: {node: '>=14.6.0'}
    peerDependencies:
      vite: ^3.0.0 || ^4.0.0

  '@webassemblyjs/ast@1.11.6':
    resolution: {integrity: sha512-IN1xI7PwOvLPgjcf180gC1bqn3q/QaOCwYUahIOhbYUu8KA/3tw2RT/T0Gidi1l7Hhj5D/INhJxiICObqpMu4Q==}

  '@webassemblyjs/floating-point-hex-parser@1.11.6':
    resolution: {integrity: sha512-ejAj9hfRJ2XMsNHk/v6Fu2dGS+i4UaXBXGemOfQ/JfQ6mdQg/WXtwleQRLLS4OvfDhv8rYnVwH27YJLMyYsxhw==}

  '@webassemblyjs/helper-api-error@1.11.6':
    resolution: {integrity: sha512-o0YkoP4pVu4rN8aTJgAyj9hC2Sv5UlkzCHhxqWj8butaLvnpdc2jOwh4ewE6CX0txSfLn/UYaV/pheS2Txg//Q==}

  '@webassemblyjs/helper-buffer@1.11.6':
    resolution: {integrity: sha512-z3nFzdcp1mb8nEOFFk8DrYLpHvhKC3grJD2ardfKOzmbmJvEf/tPIqCY+sNcwZIY8ZD7IkB2l7/pqhUhqm7hLA==}

  '@webassemblyjs/helper-numbers@1.11.6':
    resolution: {integrity: sha512-vUIhZ8LZoIWHBohiEObxVm6hwP034jwmc9kuq5GdHZH0wiLVLIPcMCdpJzG4C11cHoQ25TFIQj9kaVADVX7N3g==}

  '@webassemblyjs/helper-wasm-bytecode@1.11.6':
    resolution: {integrity: sha512-sFFHKwcmBprO9e7Icf0+gddyWYDViL8bpPjJJl0WHxCdETktXdmtWLGVzoHbqUcY4Be1LkNfwTmXOJUFZYSJdA==}

  '@webassemblyjs/helper-wasm-section@1.11.6':
    resolution: {integrity: sha512-LPpZbSOwTpEC2cgn4hTydySy1Ke+XEu+ETXuoyvuyezHO3Kjdu90KK95Sh9xTbmjrCsUwvWwCOQQNta37VrS9g==}

  '@webassemblyjs/ieee754@1.11.6':
    resolution: {integrity: sha512-LM4p2csPNvbij6U1f19v6WR56QZ8JcHg3QIJTlSwzFcmx6WSORicYj6I63f9yU1kEUtrpG+kjkiIAkevHpDXrg==}

  '@webassemblyjs/leb128@1.11.6':
    resolution: {integrity: sha512-m7a0FhE67DQXgouf1tbN5XQcdWoNgaAuoULHIfGFIEVKA6tu/edls6XnIlkmS6FrXAquJRPni3ZZKjw6FSPjPQ==}

  '@webassemblyjs/utf8@1.11.6':
    resolution: {integrity: sha512-vtXf2wTQ3+up9Zsg8sa2yWiQpzSsMyXj0qViVP6xKGCUT8p8YJ6HqI7l5eCnWx1T/FYdsv07HQs2wTFbbof/RA==}

  '@webassemblyjs/wasm-edit@1.11.6':
    resolution: {integrity: sha512-Ybn2I6fnfIGuCR+Faaz7YcvtBKxvoLV3Lebn1tM4o/IAJzmi9AWYIPWpyBfU8cC+JxAO57bk4+zdsTjJR+VTOw==}

  '@webassemblyjs/wasm-gen@1.11.6':
    resolution: {integrity: sha512-3XOqkZP/y6B4F0PBAXvI1/bky7GryoogUtfwExeP/v7Nzwo1QLcq5oQmpKlftZLbT+ERUOAZVQjuNVak6UXjPA==}

  '@webassemblyjs/wasm-opt@1.11.6':
    resolution: {integrity: sha512-cOrKuLRE7PCe6AsOVl7WasYf3wbSo4CeOk6PkrjS7g57MFfVUF9u6ysQBBODX0LdgSvQqRiGz3CXvIDKcPNy4g==}

  '@webassemblyjs/wasm-parser@1.11.6':
    resolution: {integrity: sha512-6ZwPeGzMJM3Dqp3hCsLgESxBGtT/OeCvCZ4TA1JUPYgmhAx38tTPR9JaKy0S5H3evQpO/h2uWs2j6Yc/fjkpTQ==}

  '@webassemblyjs/wast-printer@1.11.6':
    resolution: {integrity: sha512-JM7AhRcE+yW2GWYaKeHL5vt4xqee5N2WcezptmgyhNS+ScggqcT1OtXykhAb13Sn5Yas0j2uv9tHgrjwvzAP4A==}

  '@wessberg/ts-evaluator@0.0.27':
    resolution: {integrity: sha512-7gOpVm3yYojUp/Yn7F4ZybJRxyqfMNf0LXK5KJiawbPfL0XTsJV+0mgrEDjOIR6Bi0OYk2Cyg4tjFu1r8MCZaA==}
    engines: {node: '>=10.1.0'}
    deprecated: this package has been renamed to ts-evaluator. Please install ts-evaluator instead
    peerDependencies:
      typescript: '>=3.2.x || >= 4.x'

  '@xtuc/ieee754@1.2.0':
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==}

  '@xtuc/long@4.2.2':
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==}

  '@yarnpkg/lockfile@1.1.0':
    resolution: {integrity: sha512-GpSwvyXOcOOlV70vbnzjj4fW5xW/FdUF6nQEt1ENy7m4ZCczi1+/buVUPAqmGfqznsORNFzUMjctTIp8a9tuCQ==}

  abab@2.0.6:
    resolution: {integrity: sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==}
    deprecated: Use your platform's native atob() and btoa() methods instead

  abbrev@1.1.1:
    resolution: {integrity: sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==}

  accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==}
    engines: {node: '>= 0.6'}

  acorn-globals@6.0.0:
    resolution: {integrity: sha512-ZQl7LOWaF5ePqqcX4hLuv/bLXYQNfNWw2c0/yX/TsPRKamzHcTGQnlCjHT3TsmkOUVEPS3crCxiPfdzE/Trlhg==}

  acorn-import-assertions@1.9.0:
    resolution: {integrity: sha512-cmMwop9x+8KFhxvKrKfPYmN6/pKTYYHBqLa0DfvVZcKMJWNyWLnaqND7dx/qn66R7ewM1UX5XMaDVP5wlVTaVA==}
    peerDependencies:
      acorn: ^8

  acorn-walk@7.2.0:
    resolution: {integrity: sha512-OPdCF6GsMIP+Az+aWfAAOEt2/+iVDKE7oy6lJ098aoe59oAmK76qV6Gw60SbZ8jHuG2wH058GF4pLFbYamYrVA==}
    engines: {node: '>=0.4.0'}

  acorn@7.4.1:
    resolution: {integrity: sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  acorn@8.11.2:
    resolution: {integrity: sha512-nc0Axzp/0FILLEVsm4fNwLCwMttvhEI263QtVPQcbpfZZ3ts0hLsZGOpE6czNlid7CJ9MlyH8reXkpsf3YUY4w==}
    engines: {node: '>=0.4.0'}
    hasBin: true

  adjust-sourcemap-loader@4.0.0:
    resolution: {integrity: sha512-OXwN5b9pCUXNQHJpwwD2qP40byEmSgzj8B4ydSN0uMNYWiFmJ6x6KwUllMmfk8Rwu/HJDFR7U8ubsWBoN0Xp0A==}
    engines: {node: '>=8.9'}

  agent-base@6.0.2:
    resolution: {integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==}
    engines: {node: '>= 6.0.0'}

  agentkeepalive@4.5.0:
    resolution: {integrity: sha512-5GG/5IbQQpC9FpkRGsSvZI5QYeSCzlJHdpBQntCsuTOxhKD8lqKhrleg2Yi7yvMIf82Ycmmqln9U8V9qwEiJew==}
    engines: {node: '>= 8.0.0'}

  aggregate-error@3.1.0:
    resolution: {integrity: sha512-4I7Td01quW/RpocfNayFdFVk1qSuoh0E7JrbRJ16nH01HhKFQ88INq9Sd+nd72zqRySlr9BmDA8xlEJ6vJMrYA==}
    engines: {node: '>=8'}

  ajv-formats@2.1.1:
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==}
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true

  ajv-keywords@3.5.2:
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==}
    peerDependencies:
      ajv: ^6.9.1

  ajv-keywords@5.1.0:
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==}
    peerDependencies:
      ajv: ^8.8.2

  ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==}

  ajv@8.12.0:
    resolution: {integrity: sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==}

  angular-calendar@0.31.1:
    resolution: {integrity: sha512-pjSIpoAaUzS/gx+14eOr4hPZhlQ8HxpiZypCSGqJNptq5PD+vOdVQ3h/Aaqnk86GraVcAQPXqfu64MtdKwTVNw==}
    peerDependencies:
      '@angular/core': '>=15.0.0'

  angular-draggable-droppable@8.0.0:
    resolution: {integrity: sha512-+gpSNBbygjV1pxTxsM3UPJKcXHXJabYoTtKcgQe74rGnb1umKc07XCBD1qDzvlG/kocthvhQ12qfYOYzHnE3ZA==}
    peerDependencies:
      '@angular/core': '>=15.0.0'

  angular-resizable-element@7.0.2:
    resolution: {integrity: sha512-/BGuNiA38n9klexHO1xgnsA3VYigj9v+jUGjKtBRgfB26bCxZKsNWParSu2k3EqbATrfAJC4Nl8f7cORpJFf4w==}
    peerDependencies:
      '@angular/core': '>=15.0.0'

  ansi-colors@4.1.3:
    resolution: {integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==}
    engines: {node: '>=6'}

  ansi-escapes@4.3.2:
    resolution: {integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==}
    engines: {node: '>=8'}

  ansi-html-community@0.0.8:
    resolution: {integrity: sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==}
    engines: {'0': node >= 0.8.0}
    hasBin: true

  ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==}
    engines: {node: '>=8'}

  ansi-regex@6.0.1:
    resolution: {integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==}
    engines: {node: '>=12'}

  ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==}
    engines: {node: '>=4'}

  ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==}
    engines: {node: '>=8'}

  ansi-styles@6.2.1:
    resolution: {integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==}
    engines: {node: '>=12'}

  anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==}
    engines: {node: '>= 8'}

  aproba@2.0.0:
    resolution: {integrity: sha512-lYe4Gx7QT+MKGbDsA+Z+he/Wtef0BiwDOlK/XkBrdfsh9J/jPPXbX0tE9x9cl27Tmu5gg3QUbUrQYa/y+KOHPQ==}

  are-we-there-yet@3.0.1:
    resolution: {integrity: sha512-QZW4EDmGwlYur0Yyf/b2uGucHQMa8aFUP7eu9ddR73vvhFyt4V0Vl3QHPcTNJ8l6qYOBdxgXdnBXQrHilfRQBg==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This package is no longer supported.

  argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==}

  argparse@2.0.1:
    resolution: {integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==}

  array-flatten@1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==}

  array-flatten@2.1.2:
    resolution: {integrity: sha512-hNfzcOV8W4NdualtqBFPyVO+54DSJuZGY9qT4pRroB6S9e3iiido2ISIC5h9R2sPJ8H3FHCIiEnsv1lPXO3KtQ==}

  async-each-series@0.1.1:
    resolution: {integrity: sha512-p4jj6Fws4Iy2m0iCmI2am2ZNZCgbdgE+P8F/8csmn2vx7ixXrO2zGcuNsD46X5uZSVecmkEy/M06X2vG8KD6dQ==}
    engines: {node: '>=0.8.0'}

  async@2.6.4:
    resolution: {integrity: sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==}

  asynckit@0.4.0:
    resolution: {integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==}

  autoprefixer@10.4.14:
    resolution: {integrity: sha512-FQzyfOsTlwVzjHxKEqRIAdJx9niO6VCBCoEwax/VLSoQF29ggECcPuBqUMZ+u8jCZOPSy8b8/8KnuFbp0SaFZQ==}
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  axios@0.21.4:
    resolution: {integrity: sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==}

  babel-loader@9.1.3:
    resolution: {integrity: sha512-xG3ST4DglodGf8qSwv0MdeWLhrDsw/32QMdTO5T1ZIp9gQur0HkCyFs7Awskr10JKXFXwpAhiCuYX5oGXnRGbw==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
      webpack: '>=5'

  babel-plugin-istanbul@6.1.1:
    resolution: {integrity: sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==}
    engines: {node: '>=8'}

  babel-plugin-polyfill-corejs2@0.4.7:
    resolution: {integrity: sha512-LidDk/tEGDfuHW2DWh/Hgo4rmnw3cduK6ZkOI1NPFceSK3n/yAGeOsNT7FLnSGHkXj3RHGSEVkN3FsCTY6w2CQ==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.8.7:
    resolution: {integrity: sha512-KyDvZYxAzkC0Aj2dAPyDzi2Ym15e5JKZSK+maI7NAwSqofvuFglbSsxE7wUOvTg9oFVnHMzVzBKcqEb4PJgtOA==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.5.4:
    resolution: {integrity: sha512-S/x2iOCvDaCASLYsOOgWOq4bCfKYVqvO/uxjkaYyZ3rVsVE3CeAI/c84NpyuBBymEgNvHgjEot3a9/Z/kXvqsg==}
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0

  balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==}

  base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==}

  base64id@2.0.0:
    resolution: {integrity: sha512-lGe34o6EHj9y3Kts9R4ZYs/Gr+6N7MCaMlIFA3F1R2O5/m7K06AxfSeO5530PEERE6/WyEg3lsuyw4GHlPZHog==}
    engines: {node: ^4.5.0 || >= 5.9}

  batch@0.6.1:
    resolution: {integrity: sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw==}

  big.js@5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==}

  binary-extensions@2.2.0:
    resolution: {integrity: sha512-jDctJ/IVQbZoJykoeHbhXpOlNBqGNcwXJKJog42E5HDPUwQTSdjCHdihjj0DlnheQ7blbT6dHOafNAiS8ooQKA==}
    engines: {node: '>=8'}

  bl@4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==}

  body-parser@1.20.1:
    resolution: {integrity: sha512-jWi7abTbYwajOytWCQc37VulmWiRae5RyTpaCyDcS5/lMdtwSz5lOpDE67srw/HYe35f1z3fDQw+3txg7gNtWw==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  body-parser@1.20.2:
    resolution: {integrity: sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  bonjour-service@1.1.1:
    resolution: {integrity: sha512-Z/5lQRMOG9k7W+FkeGTNjh7htqn/2LMnfOvBZ8pynNZCM9MwkQkI3zeI4oz09uWdcgmgHugVvBqxGg4VQJ5PCg==}

  boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==}

  brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==}

  brace-expansion@2.0.1:
    resolution: {integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==}

  braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==}
    engines: {node: '>=8'}

  browser-process-hrtime@1.0.0:
    resolution: {integrity: sha512-9o5UecI3GhkpM6DrXr69PblIuWxPKk9Y0jHBRhdocZ2y7YECBFCsHm79Pr3OyR2AvjhDkabFJaDJMYRazHgsow==}

  browser-sync-client@2.29.3:
    resolution: {integrity: sha512-4tK5JKCl7v/3aLbmCBMzpufiYLsB1+UI+7tUXCCp5qF0AllHy/jAqYu6k7hUF3hYtlClKpxExWaR+rH+ny07wQ==}
    engines: {node: '>=8.0.0'}

  browser-sync-ui@2.29.3:
    resolution: {integrity: sha512-kBYOIQjU/D/3kYtUIJtj82e797Egk1FB2broqItkr3i4eF1qiHbFCG6srksu9gWhfmuM/TNG76jMfzAdxEPakg==}

  browser-sync@2.29.3:
    resolution: {integrity: sha512-NiM38O6XU84+MN+gzspVmXV2fTOoe+jBqIBx3IBdhZrdeURr6ZgznJr/p+hQ+KzkKEiGH/GcC4SQFSL0jV49bg==}
    engines: {node: '>= 8.0.0'}
    hasBin: true

  browserslist@4.22.2:
    resolution: {integrity: sha512-0UgcrvQmBDvZHFGdYUehrCNIazki7/lUP3kkoi/r3YB2amZbFM9J43ZRkJTXBUZK4gmx56+Sqk9+Vs9mwZx9+A==}
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true

  bs-recipes@1.3.4:
    resolution: {integrity: sha512-BXvDkqhDNxXEjeGM8LFkSbR+jzmP/CYpCiVKYn+soB1dDldeU15EBNDkwVXndKuX35wnNUaPd0qSoQEAkmQtMw==}

  buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==}

  buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==}

  builtins@5.0.1:
    resolution: {integrity: sha512-qwVpFEHNfhYJIzNRBvd2C1kyo6jz3ZSMPyyuR47OPdiKWlbYnZNyDWuyR175qDnAJLiCo5fBBqPb3RiXgWlkOQ==}

  bytes@3.0.0:
    resolution: {integrity: sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==}
    engines: {node: '>= 0.8'}

  bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==}
    engines: {node: '>= 0.8'}

  cacache@16.1.3:
    resolution: {integrity: sha512-/+Emcj9DAXxX4cwlLmRI9c166RuL3w30zp4R7Joiv2cQTtTtA+jeuCAjH3ZlGnYS3tKENSrKhAzVVP9GVyzeYQ==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  cacache@17.1.4:
    resolution: {integrity: sha512-/aJwG2l3ZMJ1xNAnqbMpA40of9dj/pIH3QfiuQSqjfPJF747VR0J/bHn+/KdNnHKc6XQcWt/AfRSBft82W1d2A==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  calendar-utils@0.10.4:
    resolution: {integrity: sha512-gBK4xCJ42yjaUKwuUha6cZOfxAmGzvSgbdAaX3xLRioeKbYoOK1x1qeD6dch72rsMZlTgATPbBBx42bnkStqgQ==}

  call-bind@1.0.5:
    resolution: {integrity: sha512-C3nQxfFZxFRVoJoGKKI8y3MOEo129NQ+FgQ08iye+Mk4zNZZGdjfs06bVTr+DBSlA66Q2VEcMki/cUCP4SercQ==}

  callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==}
    engines: {node: '>=6'}

  camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==}
    engines: {node: '>=6'}

  caniuse-lite@1.0.30001571:
    resolution: {integrity: sha512-tYq/6MoXhdezDLFZuCO/TKboTzuQ/xR5cFdgXPfDtM7/kchBO3b4VWghE/OAi/DV7tTdhmLjZiZBZi1fA/GheQ==}

  chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==}
    engines: {node: '>=4'}

  chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==}
    engines: {node: '>=10'}

  chardet@0.7.0:
    resolution: {integrity: sha512-mT8iDcrh03qDGRRmoA2hmBJnxpllMR+0/0qlzjqZES6NdiWDcZkCNAk4rPFZ9Q85r27unkiNNg8ZOiwZXBHwcA==}

  chart.js@3.9.1:
    resolution: {integrity: sha512-Ro2JbLmvg83gXF5F4sniaQ+lTbSv18E+TIf2cOeiH1Iqd2PGFOtem+DUufMZsCJwFE7ywPOpfXFBwRTGq7dh6w==}

  chokidar@3.5.3:
    resolution: {integrity: sha512-Dr3sfKRP6oTcjf2JmUmFJfeVMvXBdegxB0iVQ5eb2V10uFJUCAS8OByZdVAyVb8xXNz3GjjTgj9kLWsZTqE6kw==}
    engines: {node: '>= 8.10.0'}

  chownr@2.0.0:
    resolution: {integrity: sha512-bIomtDF5KGpdogkLd9VspvFzk9KfpyyGlS8YFVZl7TGPBHL5snIOnxeshwVgPteQ9b4Eydl+pVbIyE1DcvCWgQ==}
    engines: {node: '>=10'}

  chrome-trace-event@1.0.3:
    resolution: {integrity: sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==}
    engines: {node: '>=6.0'}

  clean-stack@2.2.0:
    resolution: {integrity: sha512-4diC9HaTE+KRAMWhDhrGOECgWZxoevMc5TlkObMqNSsVU62PYzXZ/SMTjzyGAFF1YusgxGcSWTEXBhp0CPwQ1A==}
    engines: {node: '>=6'}

  cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==}
    engines: {node: '>=8'}

  cli-spinners@2.9.2:
    resolution: {integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==}
    engines: {node: '>=6'}

  cli-width@3.0.0:
    resolution: {integrity: sha512-FxqpkPPwu1HjuN93Omfm4h8uIanXofW0RxVEW3k5RKx+mJJYSthzNhp32Kzxxy3YAEZ/Dc/EWN1vZRY0+kOhbw==}
    engines: {node: '>= 10'}

  cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==}

  cliui@8.0.1:
    resolution: {integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==}
    engines: {node: '>=12'}

  clone-deep@4.0.1:
    resolution: {integrity: sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==}
    engines: {node: '>=6'}

  clone@1.0.4:
    resolution: {integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==}
    engines: {node: '>=0.8'}

  color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==}

  color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==}
    engines: {node: '>=7.0.0'}

  color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==}

  color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==}

  color-support@1.1.3:
    resolution: {integrity: sha512-qiBjkpbMLO/HL68y+lh4q0/O1MZFj2RX6X/KmMa3+gJD3z+WwI1ZzDHysvqHGS3mP6mznPckpXmw1nI9cJjyRg==}
    hasBin: true

  colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==}

  combined-stream@1.0.8:
    resolution: {integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==}
    engines: {node: '>= 0.8'}

  commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==}

  common-path-prefix@3.0.0:
    resolution: {integrity: sha512-QE33hToZseCH3jS0qN96O/bSh3kaw/h+Tq7ngyY9eWDUnTlTNUyqfqvCXioLe5Na5jFsL78ra/wuBU4iuEgd4w==}

  compressible@2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==}
    engines: {node: '>= 0.6'}

  compression@1.7.4:
    resolution: {integrity: sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==}
    engines: {node: '>= 0.8.0'}

  concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==}

  connect-history-api-fallback@1.6.0:
    resolution: {integrity: sha512-e54B99q/OUoH64zYYRf3HBP5z24G38h5D3qXu23JGRoigpX5Ss4r9ZnDk3g0Z8uQC2x2lPaJ+UlWBc1ZWBWdLg==}
    engines: {node: '>=0.8'}

  connect-history-api-fallback@2.0.0:
    resolution: {integrity: sha512-U73+6lQFmfiNPrYbXqr6kZ1i1wiRqXnp2nhMsINseWXO8lDau0LGEffJ8kQi4EjLZympVgRdvqjAgiZ1tgzDDA==}
    engines: {node: '>=0.8'}

  connect@3.6.6:
    resolution: {integrity: sha512-OO7axMmPpu/2XuX1+2Yrg0ddju31B6xLZMWkJ5rYBu4YRmRVlOjvlY6kw2FJKiAzyxGwnrDUAG4s1Pf0sbBMCQ==}
    engines: {node: '>= 0.10.0'}

  connect@3.7.0:
    resolution: {integrity: sha512-ZqRXc+tZukToSNmh5C2iWMSoV3X1YUcPbqEM4DkEG5tNQXrQUZCNVGGv3IuicnkMtPfGf3Xtp8WCXs295iQ1pQ==}
    engines: {node: '>= 0.10.0'}

  console-control-strings@1.1.0:
    resolution: {integrity: sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==}

  content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==}
    engines: {node: '>= 0.6'}

  content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==}
    engines: {node: '>= 0.6'}

  convert-source-map@1.9.0:
    resolution: {integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==}

  convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==}

  cookie-signature@1.0.6:
    resolution: {integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==}

  cookie@0.4.2:
    resolution: {integrity: sha512-aSWTXFzaKWkvHO1Ny/s+ePFpvKsPnjc551iI41v3ny/ow6tBG5Vd+FuqGNhh1LxOmVzOlGUriIlOaokOvhaStA==}
    engines: {node: '>= 0.6'}

  cookie@0.5.0:
    resolution: {integrity: sha512-YZ3GUyn/o8gfKJlnlX7g7xq4gyO6OSuhGPKaaGssGB2qgDUS0gPgtTvoyZLTt9Ab6dC4hfc9dV5arkvc/OCmrw==}
    engines: {node: '>= 0.6'}

  copy-anything@2.0.6:
    resolution: {integrity: sha512-1j20GZTsvKNkc4BY3NpMOM8tt///wY3FpIzozTOFO2ffuZcV61nojHXVKIy3WM+7ADCy5FVhdZYHYDdgTU0yJw==}

  copy-webpack-plugin@11.0.0:
    resolution: {integrity: sha512-fX2MWpamkW0hZxMEg0+mYnA40LTosOSa5TqZ9GYIBzyJa9C3QUaMPSE2xAi/buNr8u89SfD9wHSQVBzrRa/SOQ==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      webpack: ^5.1.0

  core-js-compat@3.34.0:
    resolution: {integrity: sha512-4ZIyeNbW/Cn1wkMMDy+mvrRUxrwFNjKwbhCfQpDd+eLgYipDqp8oGFGtLmhh18EDPKA0g3VUBYOxQGGwvWLVpA==}

  core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==}

  cors@2.8.5:
    resolution: {integrity: sha512-KIHbLJqu73RGr/hnbrO9uBeixNGuvSQjul/jdFvS/KFSIH1hWVd1ng7zOHx+YrEfInLG7q4n6GHQ9cDtxv/P6g==}
    engines: {node: '>= 0.10'}

  cosmiconfig@8.3.6:
    resolution: {integrity: sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==}
    engines: {node: '>=14'}
    peerDependencies:
      typescript: '>=4.9.5'
    peerDependenciesMeta:
      typescript:
        optional: true

  critters@0.0.20:
    resolution: {integrity: sha512-CImNRorKOl5d8TWcnAz5n5izQ6HFsvz29k327/ELy6UFcmbiZNOsinaKvzv16WZR0P6etfSWYzE47C4/56B3Uw==}

  cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==}
    engines: {node: '>= 8'}

  css-loader@6.8.1:
    resolution: {integrity: sha512-xDAXtEVGlD0gJ07iclwWVkLoZOpEvAWaSyf6W18S2pOC//K8+qUDIx8IIT3D+HjnmkJPQeesOPv5aiUaJsCM2g==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0

  css-select@5.1.0:
    resolution: {integrity: sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==}

  css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==}
    engines: {node: '>= 6'}

  cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==}
    engines: {node: '>=4'}
    hasBin: true

  cssom@0.3.8:
    resolution: {integrity: sha512-b0tGHbfegbhPJpxpiBPU2sCkigAqtM9O121le6bbOlgyV+NyGyCmVfJ6QW9eRjz8CpNfWEOYBIMIGRYkLwsIYg==}

  cssom@0.4.4:
    resolution: {integrity: sha512-p3pvU7r1MyyqbTk+WbNJIgJjG2VmTIaB10rI93LzVPrmDJKkzKYMtxxyAvQXR/NS6otuzveI7+7BBq3SjBS2mw==}

  cssstyle@2.3.0:
    resolution: {integrity: sha512-AZL67abkUzIuvcHqk7c09cezpGNcxUxU4Ioi/05xHk4DQeTkWmGYftIE6ctU6AEt+Gn4n1lDStOtj7FKycP71A==}
    engines: {node: '>=8'}

  cssstyle@3.0.0:
    resolution: {integrity: sha512-N4u2ABATi3Qplzf0hWbVCdjenim8F3ojEXpBDF5hBpjzW182MjNGLqfmQ0SkSPeQ+V86ZXgeH8aXj6kayd4jgg==}
    engines: {node: '>=14'}

  custom-event@1.0.1:
    resolution: {integrity: sha512-GAj5FOq0Hd+RsCGVJxZuKaIDXDf3h6GQoNEjFgbLLI/trgtavwUbSnZ5pVfg27DVCaWjIohryS0JFwIJyT2cMg==}

  data-urls@2.0.0:
    resolution: {integrity: sha512-X5eWTSXO/BJmpdIKCRuKUgSCgAN0OwliVK3yPKbwIWU1Tdw5BRajxlzMidvh+gwko9AfQ9zIj52pzF91Q3YAvQ==}
    engines: {node: '>=10'}

  data-urls@4.0.0:
    resolution: {integrity: sha512-/mMTei/JXPqvFqQtfyTowxmJVwr2PVAeCcDxyFf6LhoOu/09TX2OX3kb2wzi4DMXcfj4OItwDOnhl5oziPnT6g==}
    engines: {node: '>=14'}

  date-fns@3.6.0:
    resolution: {integrity: sha512-fRHTG8g/Gif+kSh50gaGEdToemgfj74aRX3swtiouboip5JDLAyDE9F11nHMIcvOaXeOC6D7SpNhi7uFyB7Uww==}

  date-format@4.0.14:
    resolution: {integrity: sha512-39BOQLs9ZjKh0/patS9nrT8wc3ioX3/eA/zgbKNopnF2wCqJEoxywwwElATYvRsXdnOxA/OQeQoFZ3rFjVajhg==}
    engines: {node: '>=4.0'}

  debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.2:
    resolution: {integrity: sha512-mOp8wKcvj7XxC78zLgw/ZA+6TSgkoE2C/ienthhRD298T7UNwAg9diBpLRxC0mOezLl4B0xV7M0cCO6P/O0Xhw==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==}
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true

  decimal.js@10.4.3:
    resolution: {integrity: sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==}

  default-gateway@6.0.3:
    resolution: {integrity: sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==}
    engines: {node: '>= 10'}

  defaults@1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==}

  define-data-property@1.1.1:
    resolution: {integrity: sha512-E7uGkTzkk1d0ByLeSc6ZsFS79Axg+m1P/VsgYsxHgiuc3tFSj+MjMIwe90FC4lOAZzNBdY7kkO2P2wKdsQ1vgQ==}
    engines: {node: '>= 0.4'}

  define-lazy-prop@2.0.0:
    resolution: {integrity: sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==}
    engines: {node: '>=8'}

  delayed-stream@1.0.0:
    resolution: {integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==}
    engines: {node: '>=0.4.0'}

  delegates@1.0.0:
    resolution: {integrity: sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==}

  depd@1.1.2:
    resolution: {integrity: sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==}
    engines: {node: '>= 0.6'}

  depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==}
    engines: {node: '>= 0.8'}

  destroy@1.0.4:
    resolution: {integrity: sha512-3NdhDuEXnfun/z7x9GOElY49LoqVHoGScmOKwmxhsS8N5Y+Z8KyPPDnaSzqWgYt/ji4mqwfTS34Htrk0zPIXVg==}

  destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==}
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}

  detect-node@2.1.0:
    resolution: {integrity: sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==}

  dev-ip@1.0.1:
    resolution: {integrity: sha512-LmVkry/oDShEgSZPNgqCIp2/TlqtExeGmymru3uCELnfyjY11IzpAproLYs+1X88fXO6DBoYP3ul2Xo2yz2j6A==}
    engines: {node: '>= 0.8.0'}
    hasBin: true

  di@0.0.1:
    resolution: {integrity: sha512-uJaamHkagcZtHPqCIHZxnFrXlunQXgBOsZSUOWwFw31QJCAbyTBoHMW75YOTur5ZNx8pIeAKgf6GWIgaqqiLhA==}

  dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==}
    engines: {node: '>=8'}

  dns-equal@1.0.0:
    resolution: {integrity: sha512-z+paD6YUQsk+AbGCEM4PrOXSss5gd66QfcVBFTKR/HpFL9jCqikS94HYwKww6fQyO7IxrIIyUu+g0Ka9tUS2Cg==}

  dns-packet@5.6.1:
    resolution: {integrity: sha512-l4gcSouhcgIKRvyy99RNVOgxXiicE+2jZoNmaNmZ6JXiGajBOJAesk1OBlJuM5k2c+eudGdLxDqXuPCKIj6kpw==}
    engines: {node: '>=6'}

  dom-serialize@2.2.1:
    resolution: {integrity: sha512-Yra4DbvoW7/Z6LBN560ZwXMjoNOSAN2wRsKFGc4iBeso+mpIA6qj1vfdf9HpMaKAqG6wXTy+1SYEzmNpKXOSsQ==}

  dom-serializer@2.0.0:
    resolution: {integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==}

  domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==}

  domexception@2.0.1:
    resolution: {integrity: sha512-yxJ2mFy/sibVQlu5qHjOkf9J3K6zgmCxgJ94u2EdvDOV09H+32LtRswEcUsmUWN72pVLOEnTSRaIVVzVQgS0dg==}
    engines: {node: '>=8'}
    deprecated: Use your platform's native DOMException instead

  domexception@4.0.0:
    resolution: {integrity: sha512-A2is4PLG+eeSfoTMA95/s4pvAoSo2mKtiM5jlHkAVewmiO8ISFTFKZjH7UAM1Atli/OT/7JHOrJRJiMKUZKYBw==}
    engines: {node: '>=12'}
    deprecated: Use your platform's native DOMException instead

  domhandler@5.0.3:
    resolution: {integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==}
    engines: {node: '>= 4'}

  domutils@3.1.0:
    resolution: {integrity: sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==}

  eastasianwidth@0.2.0:
    resolution: {integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==}

  easy-extender@2.3.4:
    resolution: {integrity: sha512-8cAwm6md1YTiPpOvDULYJL4ZS6WfM5/cTeVVh4JsvyYZAoqlRVUpHL9Gr5Fy7HA6xcSZicUia3DeAgO3Us8E+Q==}
    engines: {node: '>= 4.0.0'}

  eazy-logger@4.0.1:
    resolution: {integrity: sha512-2GSFtnnC6U4IEKhEI7+PvdxrmjJ04mdsj3wHZTFiw0tUtG4HCWzTr13ZYTk8XOGnA1xQMaDljoBOYlk3D/MMSw==}
    engines: {node: '>= 0.8.0'}

  ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==}

  electron-to-chromium@1.4.616:
    resolution: {integrity: sha512-1n7zWYh8eS0L9Uy+GskE0lkBUNK83cXTVJI0pU3mGprFsbfSdAc15VTFbo+A+Bq4pwstmL30AVcEU3Fo463lNg==}

  emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==}

  emoji-regex@9.2.2:
    resolution: {integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==}

  emojis-list@3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==}
    engines: {node: '>= 4'}

  encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==}
    engines: {node: '>= 0.8'}

  encoding@0.1.13:
    resolution: {integrity: sha512-ETBauow1T35Y/WZMkio9jiM0Z5xjHHmJ4XmjZOq1l/dXz3lr2sRn87nJy20RupqSh1F2m3HHPSp8ShIPQJrJ3A==}

  engine.io-client@6.5.3:
    resolution: {integrity: sha512-9Z0qLB0NIisTRt1DZ/8U2k12RJn8yls/nXMZLn+/N8hANT3TcYjKFKcwbw5zFQiN4NTde3TSY9zb79e1ij6j9Q==}

  engine.io-parser@5.2.1:
    resolution: {integrity: sha512-9JktcM3u18nU9N2Lz3bWeBgxVgOKpw7yhRaoxQA3FUDZzzw+9WlA6p4G4u0RixNkg14fH7EfEc/RhpurtiROTQ==}
    engines: {node: '>=10.0.0'}

  engine.io@6.5.4:
    resolution: {integrity: sha512-KdVSDKhVKyOi+r5uEabrDLZw2qXStVvCsEB/LN3mw4WFi6Gx50jTyuxYVCwAAC0U46FdnzP/ScKRBTXb/NiEOg==}
    engines: {node: '>=10.2.0'}

  enhanced-resolve@5.15.0:
    resolution: {integrity: sha512-LXYT42KJ7lpIKECr2mAXIaMldcNCh/7E0KBKOu4KSfkHmP+mZmSs+8V5gBAqisWBy0OO4W5Oyys0GO1Y8KtdKg==}
    engines: {node: '>=10.13.0'}

  ent@2.2.0:
    resolution: {integrity: sha512-GHrMyVZQWvTIdDtpiEXdHZnFQKzeO09apj8Cbl4pKWy4i0Oprcq17usfDt5aO63swf0JOeMWjWQE/LzgSRuWpA==}

  entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==}
    engines: {node: '>=0.12'}

  env-paths@2.2.1:
    resolution: {integrity: sha512-+h1lkLKhZMTYjog1VEpJNG7NZJWcuc2DDk/qsqSTRRCOXiLjeQ1d1/udrUGhqMxUgAlwKNZ0cf2uqan5GLuS2A==}
    engines: {node: '>=6'}

  err-code@2.0.3:
    resolution: {integrity: sha512-2bmlRpNKBxT/CRmPOlyISQpNj+qSeYvcym/uT0Jx2bMOlKLtSy1ZmLuVxSEKKyor/N5yhvp/ZiG1oE3DEYMSFA==}

  errno@0.1.8:
    resolution: {integrity: sha512-dJ6oBr5SQ1VSd9qkk7ByRgb/1SH4JZjCHSW/mr63/QcXO9zLVxvJ6Oy13nio03rxpSnVDDjFor75SjVeZWPW/A==}
    hasBin: true

  error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==}

  es-module-lexer@1.4.1:
    resolution: {integrity: sha512-cXLGjP0c4T3flZJKQSuziYoq7MlT+rnvfZjfp7h+I7K9BNX54kP9nyWvdbwjQ4u1iWbOL4u96fgeZLToQlZC7w==}

  esbuild-wasm@0.18.17:
    resolution: {integrity: sha512-9OHGcuRzy+I8ziF9FzjfKLWAPbvi0e/metACVg9k6bK+SI4FFxeV6PcZsz8RIVaMD4YNehw+qj6UMR3+qj/EuQ==}
    engines: {node: '>=12'}
    hasBin: true

  esbuild@0.18.17:
    resolution: {integrity: sha512-1GJtYnUxsJreHYA0Y+iQz2UEykonY66HNWOb0yXYZi9/kNrORUEHVg87eQsCtqh59PEJ5YVZJO98JHznMJSWjg==}
    engines: {node: '>=12'}
    hasBin: true

  escalade@3.1.1:
    resolution: {integrity: sha512-k0er2gUkLf8O0zKJiAhmkTnJlTvINGv7ygDNPbeIsX/TJjGJZHuh9B2UxbsaEkmlEo9MfhrSzmhIlhRlI2GXnw==}
    engines: {node: '>=6'}

  escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==}

  escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==}
    engines: {node: '>=0.8.0'}

  escodegen@2.1.0:
    resolution: {integrity: sha512-2NlIDTwUWJN0mRPQOdtQBzbUHvdGY2P1VXSyU83Q3xKxM7WHX2Ql8dKq782Q9TgQUNOLEzEYu9bzLNj1q88I5w==}
    engines: {node: '>=6.0'}
    hasBin: true

  eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==}
    engines: {node: '>=8.0.0'}

  esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==}
    engines: {node: '>=4'}
    hasBin: true

  esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==}
    engines: {node: '>=4.0'}

  estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==}
    engines: {node: '>=4.0'}

  estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==}
    engines: {node: '>=4.0'}

  esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==}
    engines: {node: '>=0.10.0'}

  etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==}
    engines: {node: '>= 0.6'}

  eventemitter-asyncresource@1.0.0:
    resolution: {integrity: sha512-39F7TBIV0G7gTelxwbEqnwhp90eqCPON1k0NwNfwhgKn4Co4ybUbj2pECcXT0B3ztRKZ7Pw1JujUUgmQJHcVAQ==}

  eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==}

  events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==}
    engines: {node: '>=0.8.x'}

  execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==}
    engines: {node: '>=10'}

  exponential-backoff@3.1.1:
    resolution: {integrity: sha512-dX7e/LHVJ6W3DE1MHWi9S1EYzDESENfLrYohG2G++ovZrYOkm4Knwa0mc1cn84xJOR4KEU0WSchhLbd0UklbHw==}

  express@4.18.2:
    resolution: {integrity: sha512-5/PsL6iGPdfQ/lKM1UuielYgv3BUoJfz1aUwU9vHZ+J7gyvwdQXFEBIEIaxeGf0GIcreATNyBExtalisDbuMqQ==}
    engines: {node: '>= 0.10.0'}

  extend@3.0.2:
    resolution: {integrity: sha512-fjquC59cD7CyW6urNXK0FBufkZcoiGG80wTuPujX590cB5Ttln20E2UB4S/WARVqhXffZl2LNgS+gQdPIIim/g==}

  external-editor@3.1.0:
    resolution: {integrity: sha512-hMQ4CX1p1izmuLYyZqLMO/qGNw10wSv9QDCPfzXfyFrOaCSSoRfqE1Kf1s5an66J5JZC62NewG+mK49jOCtQew==}
    engines: {node: '>=4'}

  fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==}

  fast-glob@3.3.1:
    resolution: {integrity: sha512-kNFPyjhh5cKjrUltxs+wFx+ZkbRaxxmZ+X0ZU31SOsxCEtP9VPgtq2teZw1DebupL5GmDaNQ6yKMMVcM41iqDg==}
    engines: {node: '>=8.6.0'}

  fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==}

  fastq@1.16.0:
    resolution: {integrity: sha512-ifCoaXsDrsdkWTtiNJX5uzHDsrck5TzfKKDcuFFTIrrc/BS076qgEIfoIy1VeZqViznfKiysPYTh/QeHtnIsYA==}

  faye-websocket@0.11.4:
    resolution: {integrity: sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==}
    engines: {node: '>=0.8.0'}

  figures@3.2.0:
    resolution: {integrity: sha512-yaduQFRKLXYOGgEn6AZau90j3ggSOyiqXU0F9JZfeXYhNa+Jk4X+s45A2zg5jns87GAFa34BBm2kXw4XpNcbdg==}
    engines: {node: '>=8'}

  fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==}
    engines: {node: '>=8'}

  finalhandler@1.1.0:
    resolution: {integrity: sha512-ejnvM9ZXYzp6PUPUyQBMBf0Co5VX2gr5H2VQe2Ui2jWXNlxv+PYZo8wpAymJNJdLsG1R4p+M4aynF8KuoUEwRw==}
    engines: {node: '>= 0.8'}

  finalhandler@1.1.2:
    resolution: {integrity: sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==}
    engines: {node: '>= 0.8'}

  finalhandler@1.2.0:
    resolution: {integrity: sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==}
    engines: {node: '>= 0.8'}

  find-cache-dir@4.0.0:
    resolution: {integrity: sha512-9ZonPT4ZAK4a+1pUPVPZJapbi7O5qbbJPdYw/NOQWZZbVLdDTYM3A4R9z/DpAM08IDaFGsvPgiGZ82WEwUDWjg==}
    engines: {node: '>=14.16'}

  find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==}
    engines: {node: '>=8'}

  find-up@6.3.0:
    resolution: {integrity: sha512-v2ZsoEuVHYy8ZIlYqwPe/39Cy+cFDzp4dXPaxNvkEuouymu+2Jbz0PxpKarJHYJTmv2HWT3O382qY8l4jMWthw==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  flatted@3.2.9:
    resolution: {integrity: sha512-36yxDn5H7OFZQla0/jFJmbIKTdZAQHngCedGxiMmpNfEZM0sdEeT+WczLQrjK6D7o2aiyLYDnkw0R3JK0Qv1RQ==}

  follow-redirects@1.15.3:
    resolution: {integrity: sha512-1VzOtuEM8pC9SFU1E+8KfTjZyMztRsgEfwQl44z8A25uy13jSzTj6dyK2Df52iV0vgHCfBwLhDWevLn95w5v6Q==}
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true

  foreground-child@3.1.1:
    resolution: {integrity: sha512-TMKDUnIte6bfb5nWv7V/caI169OHgvwjb7V4WkeUvbQQdjr5rWKqHFiKWb/fcOwB+CzBT+qbWjvj+DVwRskpIg==}
    engines: {node: '>=14'}

  form-data@3.0.1:
    resolution: {integrity: sha512-RHkBKtLWUVwd7SqRIvCZMEvAMoGUp0XU+seQiZejj0COz3RI3hWP4sCv3gZWWLjJTd7rGwcsF5eKZGii0r/hbg==}
    engines: {node: '>= 6'}

  form-data@4.0.0:
    resolution: {integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==}
    engines: {node: '>= 6'}

  forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==}
    engines: {node: '>= 0.6'}

  fraction.js@4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==}

  fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==}
    engines: {node: '>= 0.6'}

  fs-extra@3.0.1:
    resolution: {integrity: sha512-V3Z3WZWVUYd8hoCL5xfXJCaHWYzmtwW5XWYSlLgERi8PWd8bx1kUHUk8L1BT57e49oKnDDD180mjfrHc1yA9rg==}

  fs-extra@8.1.0:
    resolution: {integrity: sha512-yhlQgA6mnOJUKOsRUFsgJdQCvkKhcz8tlZG5HBQfReYZy46OwLcY+Zia0mtdHsOo9y/hP+CxMN0TU9QxoOtG4g==}
    engines: {node: '>=6 <7 || >=8'}

  fs-minipass@2.1.0:
    resolution: {integrity: sha512-V/JgOLFCS+R6Vcq0slCuaeWEdNC3ouDlJMNIsacH2VtALiu9mV4LPrHc5cDl8k5aw6J8jwgWWpiTo5RYhmIzvg==}
    engines: {node: '>= 8'}

  fs-minipass@3.0.3:
    resolution: {integrity: sha512-XUBA9XClHbnJWSfBzjkm6RvPsyg3sryZt06BEQoXcF7EK/xpGaQYJgQKDJSUH5SGZ76Y7pFx1QBnXz09rU5Fbw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  fs-monkey@1.0.5:
    resolution: {integrity: sha512-8uMbBjrhzW76TYgEV27Y5E//W2f/lTFmx78P2w19FZSxarhI/798APGQyuGCwmkNxgwGRhrLfvWyLBvNtuOmew==}

  fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==}

  fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==}
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]

  function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==}

  gauge@4.0.4:
    resolution: {integrity: sha512-f9m+BEN5jkg6a0fZjleidjN51VE1X+mPFQ2DJ0uv1V39oCLCbsGe6yjbBnp7eK7z/+GAon99a3nHuqbuuthyPg==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This package is no longer supported.

  gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==}
    engines: {node: '>=6.9.0'}

  get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==}
    engines: {node: 6.* || 8.* || >= 10.*}

  get-intrinsic@1.2.2:
    resolution: {integrity: sha512-0gSo4ml/0j98Y3lngkFEot/zhiCeWsbYIlZ+uZOVgzLyLaUw7wxUL+nCTP0XJvJg1AXulJRI3UJi8GsbDuxdGA==}

  get-package-type@0.1.0:
    resolution: {integrity: sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==}
    engines: {node: '>=8.0.0'}

  get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==}
    engines: {node: '>=10'}

  glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==}
    engines: {node: '>= 6'}

  glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==}
    engines: {node: '>=10.13.0'}

  glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==}

  glob@10.3.10:
    resolution: {integrity: sha512-fa46+tv1Ak0UPK1TOy/pZrIybNNt4HCv7SDzwyfiOZkvZLEbjsZkJBPtDHVshZjbecAoAGSC20MjLDG/qr679g==}
    engines: {node: '>=16 || 14 >=14.17'}
    hasBin: true

  glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==}

  glob@8.1.0:
    resolution: {integrity: sha512-r8hpEjiQEYlF2QU0df3dS+nxxSIreXQS1qRhMJM0Q5NDdR386C7jb7Hwwod8Fgiuex+k0GFjgft18yvxm5XoCQ==}
    engines: {node: '>=12'}
    deprecated: Glob versions prior to v9 are no longer supported

  globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==}
    engines: {node: '>=4'}

  globby@13.2.2:
    resolution: {integrity: sha512-Y1zNGV+pzQdh7H39l9zgB4PJqjRNqydvdYCDG4HFXM4XuvSaQQlEc91IU1yALL8gUTDomgBAfz3XJdmUS+oo0w==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==}

  graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==}

  gsap@3.13.0:
    resolution: {integrity: sha512-QL7MJ2WMjm1PHWsoFrAQH/J8wUeqZvMtHO58qdekHpCfhvhSL4gSiz6vJf5EeMP0LOn3ZCprL2ki/gjED8ghVw==}

  guess-parser@0.4.22:
    resolution: {integrity: sha512-KcUWZ5ACGaBM69SbqwVIuWGoSAgD+9iJnchR9j/IarVI1jHVeXv+bUXBIMeqVMSKt3zrn0Dgf9UpcOEpPBLbSg==}
    peerDependencies:
      typescript: '>=3.7.5'

  handle-thing@2.0.1:
    resolution: {integrity: sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==}

  has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==}
    engines: {node: '>=4'}

  has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==}
    engines: {node: '>=8'}

  has-property-descriptors@1.0.1:
    resolution: {integrity: sha512-VsX8eaIewvas0xnvinAe9bw4WfIeODpGYikiWYLH+dma0Jw6KHYqWiWfhQlgOVK8D6PvjubK5Uc4P0iIhIcNVg==}

  has-proto@1.0.1:
    resolution: {integrity: sha512-7qE+iP+O+bgF9clE5+UoBFzE65mlBiVj3tKCrlNQ0Ogwm0BjpT/gK4SlLYDMybDh5I3TCTKnPPa0oMG7JDYrhg==}
    engines: {node: '>= 0.4'}

  has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==}
    engines: {node: '>= 0.4'}

  has-unicode@2.0.1:
    resolution: {integrity: sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==}

  hasown@2.0.0:
    resolution: {integrity: sha512-vUptKVTpIJhcczKBbgnS+RtcuYMB8+oNzPK2/Hp3hanz8JmpATdmmgLgSaadVREkDm+e2giHwY3ZRkyjSIDDFA==}
    engines: {node: '>= 0.4'}

  hdr-histogram-js@2.0.3:
    resolution: {integrity: sha512-Hkn78wwzWHNCp2uarhzQ2SGFLU3JY8SBDDd3TAABK4fc30wm+MuPOrg5QVFVfkKOQd6Bfz3ukJEI+q9sXEkK1g==}

  hdr-histogram-percentiles-obj@3.0.0:
    resolution: {integrity: sha512-7kIufnBqdsBGcSZLPJwqHT3yhk1QTsSlFsVD3kx5ixH/AlgBs9yM1q6DPhXZ8f8gtdqgh7N7/5btRLpQsS2gHw==}

  hosted-git-info@6.1.1:
    resolution: {integrity: sha512-r0EI+HBMcXadMrugk0GCQ+6BQV39PiWAZVfq7oIckeGiN7sjRGyQxPdft3nQekFTCQbYxLBH+/axZMeH8UX6+w==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  hpack.js@2.1.6:
    resolution: {integrity: sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ==}

  html-encoding-sniffer@2.0.1:
    resolution: {integrity: sha512-D5JbOMBIR/TVZkubHT+OyT2705QvogUW4IBn6nHd756OwieSF9aDYFj4dv6HHEVGYbHaLETa3WggZYWWMyy3ZQ==}
    engines: {node: '>=10'}

  html-encoding-sniffer@3.0.0:
    resolution: {integrity: sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==}
    engines: {node: '>=12'}

  html-entities@2.4.0:
    resolution: {integrity: sha512-igBTJcNNNhvZFRtm8uA6xMY6xYleeDwn3PeBCkDz7tHttv4F2hsDI2aPgNERWzvRcNYHNT3ymRaQzllmXj4YsQ==}

  html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==}

  htmlparser2@8.0.2:
    resolution: {integrity: sha512-GYdjWKDkbRLkZ5geuHs5NY1puJ+PXwP7+fHPRz06Eirsb9ugf6d8kkXav6ADhcODhFFPMIXyxkxSuMf3D6NCFA==}

  http-cache-semantics@4.1.1:
    resolution: {integrity: sha512-er295DKPVsV82j5kw1Gjt+ADA/XYHsajl82cGNQG2eyoPkvgUhX+nDIyelzhIWbbsXP39EHcI6l5tYs2FYqYXQ==}

  http-deceiver@1.2.7:
    resolution: {integrity: sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw==}

  http-errors@1.6.3:
    resolution: {integrity: sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==}
    engines: {node: '>= 0.6'}

  http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==}
    engines: {node: '>= 0.8'}

  http-parser-js@0.5.8:
    resolution: {integrity: sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==}

  http-proxy-agent@4.0.1:
    resolution: {integrity: sha512-k0zdNgqWTGA6aeIRVpvfVob4fL52dTfaehylg0Y4UvSySvOq/Y+BOyPrgpUrA7HylqvU8vIZGsRuXmspskV0Tg==}
    engines: {node: '>= 6'}

  http-proxy-agent@5.0.0:
    resolution: {integrity: sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==}
    engines: {node: '>= 6'}

  http-proxy-middleware@2.0.6:
    resolution: {integrity: sha512-ya/UeJ6HVBYxrgYotAZo1KvPWlgB48kUJLDePFeneHsVujFaW5WNj2NgWCAE//B1Dl02BIfYlpNgBy8Kf8Rjmw==}
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/express': ^4.17.13
    peerDependenciesMeta:
      '@types/express':
        optional: true

  http-proxy@1.18.1:
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==}
    engines: {node: '>=8.0.0'}

  https-proxy-agent@5.0.1:
    resolution: {integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==}
    engines: {node: '>= 6'}

  human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==}
    engines: {node: '>=10.17.0'}

  humanize-ms@1.2.1:
    resolution: {integrity: sha512-Fl70vYtsAFb/C06PTS9dZBo7ihau+Tu/DNCk/OyHhea07S+aeMWpFFkUaXRa8fI+ScZbEI8dfSxwY7gxZ9SAVQ==}

  iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==}
    engines: {node: '>=0.10.0'}

  iconv-lite@0.6.3:
    resolution: {integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==}
    engines: {node: '>=0.10.0'}

  icss-utils@5.1.0:
    resolution: {integrity: sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==}

  ignore-walk@6.0.4:
    resolution: {integrity: sha512-t7sv42WkwFkyKbivUCglsQW5YWMskWtbEf4MNKX5u/CCWHKSPzN4FtBQGsQZgCLbxOzpVlcbWVK5KB3auIOjSw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  ignore@5.3.0:
    resolution: {integrity: sha512-g7dmpshy+gD7mh88OC9NwSGTKoc3kyLAZQRU1mt53Aw/vnvfXnbC+F/7F7QoYVKbV+KNvJx8wArewKy1vXMtlg==}
    engines: {node: '>= 4'}

  image-size@0.5.5:
    resolution: {integrity: sha512-6TDAlDPZxUFCv+fuOkIoXT/V/f3Qbq8e37p+YOiYrUv3v9cc3/6x78VdfPgFVaB9dZYeLUfKgHRebpkm/oP2VQ==}
    engines: {node: '>=0.10.0'}
    hasBin: true

  immutable@3.8.2:
    resolution: {integrity: sha512-15gZoQ38eYjEjxkorfbcgBKBL6R7T459OuK+CpcWt7O3KF4uPCx2tD0uFETlUDIyo+1789crbMhTvQBSR5yBMg==}
    engines: {node: '>=0.10.0'}

  immutable@4.3.4:
    resolution: {integrity: sha512-fsXeu4J4i6WNWSikpI88v/PcVflZz+6kMhUfIwc5SY+poQRPnaf5V7qds6SUyUN3cVxEzuCab7QIoLOQ+DQ1wA==}

  import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==}
    engines: {node: '>=6'}

  imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==}
    engines: {node: '>=0.8.19'}

  indent-string@4.0.0:
    resolution: {integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==}
    engines: {node: '>=8'}

  infer-owner@1.0.4:
    resolution: {integrity: sha512-IClj+Xz94+d7irH5qRyfJonOdfTzuDaifE6ZPWfx0N0+/ATZCbuTPq2prFl526urkQd90WyUKIh1DfBQ2hMz9A==}

  inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==}

  inherits@2.0.3:
    resolution: {integrity: sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==}

  inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==}

  ini@4.1.1:
    resolution: {integrity: sha512-QQnnxNyfvmHFIsj7gkPcYymR8Jdw/o7mp5ZFihxn6h8Ci6fh3Dx4E1gPjpQEpIuPo9XVNY/ZUwh4BPMjGyL01g==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  inquirer@8.2.4:
    resolution: {integrity: sha512-nn4F01dxU8VeKfq192IjLsxu0/OmMZ4Lg3xKAns148rCaXP6ntAoEkVYZThWjwON8AlzdZZi6oqnhNbxUG9hVg==}
    engines: {node: '>=12.0.0'}

  ip@2.0.0:
    resolution: {integrity: sha512-WKa+XuLG1A1R0UWhl2+1XQSi+fZWMsYKffMZTTYsiZaUD8k2yDAj5atimTUD2TZkyCkNEeYE5NhFZmupOGtjYQ==}

  ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==}
    engines: {node: '>= 0.10'}

  ipaddr.js@2.1.0:
    resolution: {integrity: sha512-LlbxQ7xKzfBusov6UMi4MFpEg0m+mAm9xyNGEduwXMEDuf4WfzB/RZwMVYEd7IKGvh4IUkEXYxtAVu9T3OelJQ==}
    engines: {node: '>= 10'}

  is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==}

  is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==}
    engines: {node: '>=8'}

  is-core-module@2.13.1:
    resolution: {integrity: sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==}

  is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==}
    engines: {node: '>=8'}
    hasBin: true

  is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==}
    engines: {node: '>=0.10.0'}

  is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==}
    engines: {node: '>=8'}

  is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==}
    engines: {node: '>=0.10.0'}

  is-interactive@1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==}
    engines: {node: '>=8'}

  is-lambda@1.0.1:
    resolution: {integrity: sha512-z7CMFGNrENq5iFB9Bqo64Xk6Y9sg+epq1myIcdHaGnbMTYOxvzsEtdYqQUylB7LxfkvgrrjP32T6Ywciio9UIQ==}

  is-number-like@1.0.8:
    resolution: {integrity: sha512-6rZi3ezCyFcn5L71ywzz2bS5b2Igl1En3eTlZlvKjpz1n3IZLAYMbKYAIQgFmEu0GENg92ziU/faEOA/aixjbA==}

  is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==}
    engines: {node: '>=0.12.0'}

  is-plain-obj@3.0.0:
    resolution: {integrity: sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==}
    engines: {node: '>=10'}

  is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==}
    engines: {node: '>=0.10.0'}

  is-potential-custom-element-name@1.0.1:
    resolution: {integrity: sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==}

  is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==}
    engines: {node: '>=8'}

  is-unicode-supported@0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==}
    engines: {node: '>=10'}

  is-what@3.14.1:
    resolution: {integrity: sha512-sNxgpk9793nzSs7bA6JQJGeIuRBQhAaNGG77kzYQgMkrID+lS6SlK07K5LaptscDlSaIgH+GPFzf+d75FVxozA==}

  is-wsl@1.1.0:
    resolution: {integrity: sha512-gfygJYZ2gLTDlmbWMI0CE2MwnFzSN/2SZfkMlItC4K/JBlsWVDB0bO6XhqcY13YXE7iMcAJnzTCJjPiTeJJ0Mw==}
    engines: {node: '>=4'}

  is-wsl@2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==}
    engines: {node: '>=8'}

  isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==}

  isbinaryfile@4.0.10:
    resolution: {integrity: sha512-iHrqe5shvBUcFbmZq9zOQHBoeOhZJu6RQGrDpBgenUm/Am+F3JM2MgQj+rK3Z601fzrL5gLZWtAPH2OBaSVcyw==}
    engines: {node: '>= 8.0.0'}

  isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==}

  isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==}
    engines: {node: '>=0.10.0'}

  istanbul-lib-coverage@3.2.2:
    resolution: {integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==}
    engines: {node: '>=8'}

  istanbul-lib-instrument@5.2.1:
    resolution: {integrity: sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==}
    engines: {node: '>=8'}

  istanbul-lib-report@3.0.1:
    resolution: {integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==}
    engines: {node: '>=10'}

  istanbul-lib-source-maps@4.0.1:
    resolution: {integrity: sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==}
    engines: {node: '>=10'}

  istanbul-reports@3.1.6:
    resolution: {integrity: sha512-TLgnMkKg3iTDsQ9PbPTdpfAK2DzjF9mqUG7RMgcQl8oFjad8ob4laGxv5XV5U9MAfx8D6tSJiUyuAwzLicaxlg==}
    engines: {node: '>=8'}

  jackspeak@2.3.6:
    resolution: {integrity: sha512-N3yCS/NegsOBokc8GAdM8UcmfsKiSS8cipheD/nivzr700H+nsMOxJjQnvwOcRYVuFkdH0wGUvW2WbXGmrZGbQ==}
    engines: {node: '>=14'}

  jasmine-core@4.6.0:
    resolution: {integrity: sha512-O236+gd0ZXS8YAjFx8xKaJ94/erqUliEkJTDedyE7iHvv4ZVqi+q+8acJxu05/WJDKm512EUNn809In37nWlAQ==}

  jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==}
    engines: {node: '>= 10.13.0'}

  jiti@1.21.0:
    resolution: {integrity: sha512-gFqAIbuKyyso/3G2qhiO2OM6shY6EPP/R0+mkDbyspxKazh8BXDC5FiFsUjlczgdNz/vfra0da2y+aHrusLG/Q==}
    hasBin: true

  js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==}

  js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==}
    hasBin: true

  js-yaml@4.1.0:
    resolution: {integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==}
    hasBin: true

  jsdom@16.7.0:
    resolution: {integrity: sha512-u9Smc2G1USStM+s/x1ru5Sxrl6mPYCbByG1U/hUmqaVsm4tbNyS7CicOSRyuGQYZhTu0h84qkZZQ/I+dzizSVw==}
    engines: {node: '>=10'}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsdom@22.1.0:
    resolution: {integrity: sha512-/9AVW7xNbsBv6GfWho4TTNjEo9fe6Zhf9O7s0Fhhr3u+awPwAJMKwAMXnkk5vBxflqLW9hTHX/0cs+P3gW+cQw==}
    engines: {node: '>=16'}
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@0.5.0:
    resolution: {integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==}
    hasBin: true

  jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==}
    engines: {node: '>=4'}
    hasBin: true

  json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==}

  json-parse-even-better-errors@3.0.1:
    resolution: {integrity: sha512-aatBvbL26wVUCLmbWdCpeu9iF5wOyWpagiKkInA+kfws3sWdBrTnsvN2CKcyCYyUrc7rebNBlK6+kteg7ksecg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==}

  json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==}

  json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==}
    engines: {node: '>=6'}
    hasBin: true

  jsonc-parser@3.2.0:
    resolution: {integrity: sha512-gfFQZrcTc8CnKXp6Y4/CBT3fTc0OVuDofpre4aEeEpSBPV5X5v4+Vmx+8snU7RLPrNHPKSgLxGo9YuQzz20o+w==}

  jsonfile@3.0.1:
    resolution: {integrity: sha512-oBko6ZHlubVB5mRFkur5vgYR1UyqX+S6Y/oCfLhqNdcc2fYFlDpIoNc7AfKS1KOGcnNAkvsr0grLck9ANM815w==}

  jsonfile@4.0.0:
    resolution: {integrity: sha512-m6F1R3z8jjlf2imQHS2Qez5sjKWQzbuuhuJ/FKYFRZvPE3PuHcSMVZzfsLhGVOkfd20obL5SWEBew5ShlquNxg==}

  jsonparse@1.3.1:
    resolution: {integrity: sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==}
    engines: {'0': node >= 0.2.0}

  karma-chrome-launcher@3.2.0:
    resolution: {integrity: sha512-rE9RkUPI7I9mAxByQWkGJFXfFD6lE4gC5nPuZdobf/QdTEJI6EU4yIay/cfU/xV4ZxlM5JiTv7zWYgA64NpS5Q==}

  karma-coverage@2.2.1:
    resolution: {integrity: sha512-yj7hbequkQP2qOSb20GuNSIyE//PgJWHwC2IydLE6XRtsnaflv+/OSGNssPjobYUlhVVagy99TQpqUt3vAUG7A==}
    engines: {node: '>=10.0.0'}

  karma-jasmine-html-reporter@2.1.0:
    resolution: {integrity: sha512-sPQE1+nlsn6Hwb5t+HHwyy0A1FNCVKuL1192b+XNauMYWThz2kweiBVW1DqloRpVvZIJkIoHVB7XRpK78n1xbQ==}
    peerDependencies:
      jasmine-core: ^4.0.0 || ^5.0.0
      karma: ^6.0.0
      karma-jasmine: ^5.0.0

  karma-jasmine@5.1.0:
    resolution: {integrity: sha512-i/zQLFrfEpRyQoJF9fsCdTMOF5c2dK7C7OmsuKg2D0YSsuZSfQDiLuaiktbuio6F2wiCsZSnSnieIQ0ant/uzQ==}
    engines: {node: '>=12'}
    peerDependencies:
      karma: ^6.0.0

  karma-source-map-support@1.4.0:
    resolution: {integrity: sha512-RsBECncGO17KAoJCYXjv+ckIz+Ii9NCi+9enk+rq6XC81ezYkb4/RHE6CTXdA7IOJqoF3wcaLfVG0CPmE5ca6A==}

  karma@6.4.2:
    resolution: {integrity: sha512-C6SU/53LB31BEgRg+omznBEMY4SjHU3ricV6zBcAe1EeILKkeScr+fZXtaI5WyDbkVowJxxAI6h73NcFPmXolQ==}
    engines: {node: '>= 10'}
    hasBin: true

  kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==}
    engines: {node: '>=0.10.0'}

  klona@2.0.6:
    resolution: {integrity: sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA==}
    engines: {node: '>= 8'}

  launch-editor@2.6.1:
    resolution: {integrity: sha512-eB/uXmFVpY4zezmGp5XtU21kwo7GBbKB+EQ+UZeWtGb9yAM5xt/Evk+lYH3eRNAtId+ej4u7TYPFZ07w4s7rRw==}

  less-loader@11.1.0:
    resolution: {integrity: sha512-C+uDBV7kS7W5fJlUjq5mPBeBVhYpTIm5gB09APT9o3n/ILeaXVsiSFTbZpTJCJwQ/Crczfn3DmfQFwxYusWFug==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      less: ^3.5.0 || ^4.0.0
      webpack: ^5.0.0

  less@4.1.3:
    resolution: {integrity: sha512-w16Xk/Ta9Hhyei0Gpz9m7VS8F28nieJaL/VyShID7cYvP6IL5oHeL6p4TXSDJqZE/lNv0oJ2pGVjJsRkfwm5FA==}
    engines: {node: '>=6'}
    hasBin: true

  license-webpack-plugin@4.0.2:
    resolution: {integrity: sha512-771TFWFD70G1wLTC4oU2Cw4qvtmNrIw+wRvBtn+okgHl7slJVi7zfNcdmqDL72BojM30VNJ2UHylr1o77U37Jw==}
    peerDependencies:
      webpack: '*'
    peerDependenciesMeta:
      webpack:
        optional: true

  limiter@1.1.5:
    resolution: {integrity: sha512-FWWMIEOxz3GwUI4Ts/IvgVy6LPvoMPgjMdQ185nN6psJyBJ4yOpzqm695/h5umdLJg2vW3GR5iG11MAkR2AzJA==}

  lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==}

  loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==}
    engines: {node: '>=6.11.5'}

  loader-utils@2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==}
    engines: {node: '>=8.9.0'}

  loader-utils@3.2.1:
    resolution: {integrity: sha512-ZvFw1KWS3GVyYBYb7qkmRM/WwL2TQQBxgCK62rlvm4WpVQ23Nb4tYjApUlfjrEGvOs7KHEsmyUn75OHZrJMWPw==}
    engines: {node: '>= 12.13.0'}

  localtunnel@2.0.2:
    resolution: {integrity: sha512-n418Cn5ynvJd7m/N1d9WVJISLJF/ellZnfsLnx8WBWGzxv/ntNcFkJ1o6se5quUhCplfLGBNL5tYHiq5WF3Nug==}
    engines: {node: '>=8.3.0'}
    hasBin: true

  locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==}
    engines: {node: '>=8'}

  locate-path@7.2.0:
    resolution: {integrity: sha512-gvVijfZvn7R+2qyPX8mAuKcFGDf6Nc61GdvGafQsHL0sBIxfKzA+usWn4GFC/bk+QdwPUD4kWFJLhElipq+0VA==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  lodash-es@4.17.21:
    resolution: {integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==}

  lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==}

  lodash.isfinite@3.3.2:
    resolution: {integrity: sha512-7FGG40uhC8Mm633uKW1r58aElFlBlxCrg9JfSi3P6aYiWmfiWF0PgMd86ZUsxE5GwWPdHoS2+48bwTh2VPkIQA==}

  lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==}

  log-symbols@4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==}
    engines: {node: '>=10'}

  log4js@6.9.1:
    resolution: {integrity: sha512-1somDdy9sChrr9/f4UlzhdaGfDR2c/SaD2a4T7qEkG4jTS57/B3qmnjLYePwQ8cqWnUHZI0iAKxMBpCZICiZ2g==}
    engines: {node: '>=8.0'}

  lru-cache@10.1.0:
    resolution: {integrity: sha512-/1clY/ui8CzjKFyjdvwPWJUYKiFVXG2I2cY0ssG7h4+hwk+XOIX7ZSG9Q7TW8TW3Kp3BUSqgFWBLgL4PJ+Blag==}
    engines: {node: 14 || >=16.14}

  lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==}

  lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==}
    engines: {node: '>=10'}

  lru-cache@7.18.3:
    resolution: {integrity: sha512-jumlc0BIUrS3qJGgIkWZsyfAM7NCWiBcCDhnd+3NNM5KbBmLTgHVfWBcg6W+rLUsIpzpERPsvwUP7CckAQSOoA==}
    engines: {node: '>=12'}

  magic-string@0.30.1:
    resolution: {integrity: sha512-mbVKXPmS0z0G4XqFDCTllmDQ6coZzn94aMlb0o/A4HEHJCKcanlDZwYJgwnkmgD3jyWhUgj9VsPrfd972yPffA==}
    engines: {node: '>=12'}

  make-dir@2.1.0:
    resolution: {integrity: sha512-LS9X+dc8KLxXCb8dni79fLIIUA5VyZoyjSMCwTluaXA0o27cCK0bhXkpgw+sTXVpPy/lSO57ilRixqk0vDmtRA==}
    engines: {node: '>=6'}

  make-dir@4.0.0:
    resolution: {integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==}
    engines: {node: '>=10'}

  make-fetch-happen@10.2.1:
    resolution: {integrity: sha512-NgOPbRiaQM10DYXvN3/hhGVI2M5MtITFryzBGxHM5p4wnFxsVCbxkrBrDsk+EZ5OB4jEOT7AjDxtdF+KVEFT7w==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  make-fetch-happen@11.1.1:
    resolution: {integrity: sha512-rLWS7GCSTcEujjVBs2YqG7Y4643u8ucvCJeSRqiLYhesrDuzeuFIk37xREzAsfQaqzl8b9rNCE4m6J8tvX4Q8w==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==}
    engines: {node: '>= 0.6'}

  memfs@3.5.3:
    resolution: {integrity: sha512-UERzLsxzllchadvbPs5aolHh65ISpKpM+ccLbOJ8/vvpBKmAWf+la7dXFy7Mr0ySHbdHrFv5kGFCUHHe6GFEmw==}
    engines: {node: '>= 4.0.0'}

  merge-descriptors@1.0.1:
    resolution: {integrity: sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==}

  merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==}

  merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==}
    engines: {node: '>= 8'}

  methods@1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==}
    engines: {node: '>= 0.6'}

  micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==}
    engines: {node: '>=8.6'}

  mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==}
    engines: {node: '>= 0.6'}

  mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==}
    engines: {node: '>= 0.6'}

  mime@1.4.1:
    resolution: {integrity: sha512-KI1+qOZu5DcW6wayYHSzR/tXKCDC5Om4s1z2QJjDULzLcmf3DvzS7oluY4HCTrc+9FiKmWUgeNLg7W3uIQvxtQ==}
    hasBin: true

  mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==}
    engines: {node: '>=4'}
    hasBin: true

  mime@2.6.0:
    resolution: {integrity: sha512-USPkMeET31rOMiarsBNIHZKLGgvKc/LrjofAnBlOttf5ajRvqiRA8QsenbcooctK6d6Ts6aqZXBA+XbkKthiQg==}
    engines: {node: '>=4.0.0'}
    hasBin: true

  mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==}
    engines: {node: '>=6'}

  mini-css-extract-plugin@2.7.6:
    resolution: {integrity: sha512-Qk7HcgaPkGG6eD77mLvZS1nmxlao3j+9PkrT9Uc7HAE1id3F41+DdBRYRYkbyfNRGzm8/YWtzhw7nVPmwhqTQw==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0

  minimalistic-assert@1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==}

  minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==}

  minimatch@5.1.6:
    resolution: {integrity: sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==}
    engines: {node: '>=10'}

  minimatch@9.0.3:
    resolution: {integrity: sha512-RHiac9mvaRw0x3AYRgDC1CxAP7HTcNrrECeA8YYJeWnpo+2Q5CegtZjaotWTWxDG3UeGA1coE05iH1mPjT/2mg==}
    engines: {node: '>=16 || 14 >=14.17'}

  minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==}

  minipass-collect@1.0.2:
    resolution: {integrity: sha512-6T6lH0H8OG9kITm/Jm6tdooIbogG9e0tLgpY6mphXSm/A9u8Nq1ryBG+Qspiub9LjWlBPsPS3tWQ/Botq4FdxA==}
    engines: {node: '>= 8'}

  minipass-fetch@2.1.2:
    resolution: {integrity: sha512-LT49Zi2/WMROHYoqGgdlQIZh8mLPZmOrN2NdJjMXxYe4nkN6FUyuPuOAOedNJDrx0IRGg9+4guZewtp8hE6TxA==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  minipass-fetch@3.0.4:
    resolution: {integrity: sha512-jHAqnA728uUpIaFm7NWsCnqKT6UqZz7GcI/bDpPATuwYyKwJwW0remxSCxUlKiEty+eopHGa3oc8WxgQ1FFJqg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  minipass-flush@1.0.5:
    resolution: {integrity: sha512-JmQSYYpPUqX5Jyn1mXaRwOda1uQ8HP5KAT/oDSLCzt1BYRhQU0/hDtsB1ufZfEEzMZ9aAVmsBw8+FWsIXlClWw==}
    engines: {node: '>= 8'}

  minipass-json-stream@1.0.1:
    resolution: {integrity: sha512-ODqY18UZt/I8k+b7rl2AENgbWE8IDYam+undIJONvigAz8KR5GWblsFTEfQs0WODsjbSXWlm+JHEv8Gr6Tfdbg==}

  minipass-pipeline@1.2.4:
    resolution: {integrity: sha512-xuIq7cIOt09RPRJ19gdi4b+RiNvDFYe5JH+ggNvBqGqpQXcru3PcRmOZuHBKWK1Txf9+cQ+HMVN4d6z46LZP7A==}
    engines: {node: '>=8'}

  minipass-sized@1.0.3:
    resolution: {integrity: sha512-MbkQQ2CTiBMlA2Dm/5cY+9SWFEN8pzzOXi6rlM5Xxq0Yqbda5ZQy9sU75a673FE9ZK0Zsbr6Y5iP6u9nktfg2g==}
    engines: {node: '>=8'}

  minipass@3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==}
    engines: {node: '>=8'}

  minipass@5.0.0:
    resolution: {integrity: sha512-3FnjYuehv9k6ovOEbyOswadCDPX1piCfhV8ncmYtHOjuPwylVWsghTLo7rabjC3Rx5xD4HDx8Wm1xnMF7S5qFQ==}
    engines: {node: '>=8'}

  minipass@7.0.4:
    resolution: {integrity: sha512-jYofLM5Dam9279rdkWzqHozUo4ybjdZmCsDHePy5V/PbBcVMiSZR97gmAy45aqi8CK1lG2ECd356FU86avfwUQ==}
    engines: {node: '>=16 || 14 >=14.17'}

  minizlib@2.1.2:
    resolution: {integrity: sha512-bAxsR8BVfj60DWXHE3u30oHzfl4G7khkSuPW+qvpd7jFRHm7dLxOjUk1EHACJ/hxLY8phGJ0YhYHZo7jil7Qdg==}
    engines: {node: '>= 8'}

  mitt@1.2.0:
    resolution: {integrity: sha512-r6lj77KlwqLhIUku9UWYes7KJtsczvolZkzp8hbaDPPaE24OmWl5s539Mytlj22siEQKosZ26qCBgda2PKwoJw==}

  mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==}
    hasBin: true

  mkdirp@1.0.4:
    resolution: {integrity: sha512-vVqVZQyf3WLx2Shd0qJ9xuvqgAyKPLAiqITEtqW0oIUjzo3PePDd6fW9iFz30ef7Ysp/oiWqbhszeGWW2T6Gzw==}
    engines: {node: '>=10'}
    hasBin: true

  mrmime@1.0.1:
    resolution: {integrity: sha512-hzzEagAgDyoU1Q6yg5uI+AorQgdvMCur3FcKf7NhMKWsaYg+RnbTyHRa/9IlLF9rf455MOCtcqqrQQ83pPP7Uw==}
    engines: {node: '>=10'}

  ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==}

  ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==}

  ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==}

  multicast-dns@7.2.5:
    resolution: {integrity: sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg==}
    hasBin: true

  mute-stream@0.0.8:
    resolution: {integrity: sha512-nnbWWOkoWyUsTjKrhgD0dcz22mdkSnpYqbEjIm2nhwhuxlSkpywJmBo8h0ZqJdkp73mb90SssHkN4rsRaBAfAA==}

  nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==}
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  needle@3.3.1:
    resolution: {integrity: sha512-6k0YULvhpw+RoLNiQCRKOl09Rv1dPLr8hHnVjHqdolKwDrdNyk+Hmrthi4lIGPPz3r39dLx0hsF5s40sZ3Us4Q==}
    engines: {node: '>= 4.4.x'}
    hasBin: true

  negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==}
    engines: {node: '>= 0.6'}

  neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==}

  ng2-charts@3.1.2:
    resolution: {integrity: sha512-KkbpQhbf8DhcEm5SnqKBQMa25cBOKBiopjl4+8aUbfOaKY59uOfImAYPDsT3du7cfMVD6w7xt4SWhRbbelY9sQ==}
    peerDependencies:
      '@angular/common': '>=11.0.0'
      '@angular/core': '>=11.0.0'
      chart.js: ^3.4.0
      rxjs: ^6.5.3 || ^7.4.0

  ngx-bootstrap@19.0.2:
    resolution: {integrity: sha512-DR5sA5e9k1bwtVeo8lQW4Yr8IgU7ba2pco5rlznnr7k91BJ3SNgrUSq+BQfigwARHGmgKCcw9lexu8ZWvefEXg==}
    peerDependencies:
      '@angular/animations': ^19.0.1
      '@angular/common': ^19.0.1
      '@angular/core': ^19.0.1
      '@angular/forms': ^19.0.1
      rxjs: ^6.5.3 || ^7.4.0

  ngx-loading@17.0.0:
    resolution: {integrity: sha512-dPCUXPZ0efGaPU2N0agQaceIZR5fRfzHj85rh/w4WwwN0UicsmP5ZFN4R7nccACsaNAF/bbwVHhfjHlngoFT6Q==}
    peerDependencies:
      '@angular/common': ^17.0.0
      '@angular/core': ^17.0.0

  ngx-pagination@6.0.3:
    resolution: {integrity: sha512-lONjTQ7hFPh1SyhwDrRd5ZwM4NMGQ7bNR6vLrs6mrU0Z8Q1zCcWbf/pvyp4DOlGyd9uyZxRy2wUsSZLeIPjbAw==}
    peerDependencies:
      '@angular/common': '>=13.0.0'
      '@angular/core': '>=13.0.0'

  ngx-toastr@19.0.0:
    resolution: {integrity: sha512-6pTnktwwWD+kx342wuMOWB4+bkyX9221pAgGz3SHOJH0/MI9erLucS8PeeJDFwbUYyh75nQ6AzVtolgHxi52dQ==}
    peerDependencies:
      '@angular/common': '>=16.0.0-0'
      '@angular/core': '>=16.0.0-0'
      '@angular/platform-browser': '>=16.0.0-0'

  nice-napi@1.0.2:
    resolution: {integrity: sha512-px/KnJAJZf5RuBGcfD+Sp2pAKq0ytz8j+1NehvgIGFkvtvFrDM3T8E4x/JJODXK9WZow8RRGrbA9QQ3hs+pDhA==}
    os: ['!win32']

  node-addon-api@3.2.1:
    resolution: {integrity: sha512-mmcei9JghVNDYydghQmeDX8KoAm0FAiYyIcUt/N4nhyAipB17pllZQDOJD2fotxABnt4Mdz+dKTO7eftLg4d0A==}

  node-forge@1.3.1:
    resolution: {integrity: sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==}
    engines: {node: '>= 6.13.0'}

  node-gyp-build@4.7.1:
    resolution: {integrity: sha512-wTSrZ+8lsRRa3I3H8Xr65dLWSgCvY2l4AOnaeKdPA9TB/WYMPaTcrzf3rXvFoVvjKNVnu0CcWSx54qq9GKRUYg==}
    hasBin: true

  node-gyp@9.4.1:
    resolution: {integrity: sha512-OQkWKbjQKbGkMf/xqI1jjy3oCTgMKJac58G2+bjZb3fza6gW2YrCSdMQYaoTb70crvE//Gngr4f0AgVHmqHvBQ==}
    engines: {node: ^12.13 || ^14.13 || >=16}
    hasBin: true

  node-releases@2.0.14:
    resolution: {integrity: sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==}

  nopt@6.0.0:
    resolution: {integrity: sha512-ZwLpbTgdhuZUnZzjd7nb1ZV+4DoiC6/sfiVKok72ym/4Tlf+DFdlHYmT2JPmcNNWV6Pi3SDf1kT+A4r9RTuT9g==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    hasBin: true

  normalize-package-data@5.0.0:
    resolution: {integrity: sha512-h9iPVIfrVZ9wVYQnxFgtw1ugSvGEMOlyPWWtm8BMJhnwyEL/FLbYbTY3V3PpjI/BUK67n9PEWDu6eHzu1fB15Q==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==}
    engines: {node: '>=0.10.0'}

  normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==}
    engines: {node: '>=0.10.0'}

  npm-bundled@3.0.0:
    resolution: {integrity: sha512-Vq0eyEQy+elFpzsKjMss9kxqb9tG3YHg4dsyWuUENuzvSUWe1TCnW/vV9FkhvBk/brEDoDiVd+M1Btosa6ImdQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-install-checks@6.3.0:
    resolution: {integrity: sha512-W29RiK/xtpCGqn6f3ixfRYGk+zRyr+Ew9F2E20BfXxT5/euLdA/Nm7fO7OeTGuAmTs30cpgInyJ0cYe708YTZw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-normalize-package-bin@3.0.1:
    resolution: {integrity: sha512-dMxCf+zZ+3zeQZXKxmyuCKlIDPGuv8EF940xbkC4kQVDTtqoh6rJFO+JTKSA6/Rwi0getWmtuy4Itup0AMcaDQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-package-arg@10.1.0:
    resolution: {integrity: sha512-uFyyCEmgBfZTtrKk/5xDfHp6+MdrqGotX/VoOyEEl3mBwiEE5FlBaePanazJSVMPT7vKepcjYBY2ztg9A3yPIA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-packlist@7.0.4:
    resolution: {integrity: sha512-d6RGEuRrNS5/N84iglPivjaJPxhDbZmlbTwTDX2IbcRHG5bZCdtysYMhwiPvcF4GisXHGn7xsxv+GQ7T/02M5Q==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-pick-manifest@8.0.1:
    resolution: {integrity: sha512-mRtvlBjTsJvfCCdmPtiu2bdlx8d/KXtF7yNXNWe7G0Z36qWA9Ny5zXsI2PfBZEv7SXgoxTmNaTzGSbbzDZChoA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-registry-fetch@14.0.5:
    resolution: {integrity: sha512-kIDMIo4aBm6xg7jOttupWZamsZRkAqMqwqqbVXnUqstY5+tapvv6bkH/qMR76jdgV+YljEUCyWx3hRYMrJiAgA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==}
    engines: {node: '>=8'}

  npmlog@6.0.2:
    resolution: {integrity: sha512-/vBvz5Jfr9dT/aFWd0FIRf+T/Q2WBsLENygUaFUqstqsycmZAP/t5BvFJTK0viFmSUxiUKTUplWy5vt+rvKIxg==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}
    deprecated: This package is no longer supported.

  nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==}

  nwsapi@2.2.7:
    resolution: {integrity: sha512-ub5E4+FBPKwAZx0UwIQOjYWGHTEq5sPqHQNRN8Z9e4A7u3Tj1weLJsL59yH9vmvqEtBHaOmT6cYQKIZOxp35FQ==}

  object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==}
    engines: {node: '>=0.10.0'}

  object-inspect@1.13.1:
    resolution: {integrity: sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==}

  object-path@0.11.8:
    resolution: {integrity: sha512-YJjNZrlXJFM42wTBn6zgOJVar9KFJvzx6sTWDte8sWZF//cnjl0BxHNpfZx+ZffXX63A9q0b1zsFiBX4g4X5KA==}
    engines: {node: '>= 10.12.0'}

  obuf@1.1.2:
    resolution: {integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==}

  on-finished@2.3.0:
    resolution: {integrity: sha512-ikqdkGAAyf/X/gPhXGvfgAytDZtDbr+bkNUJ0N9h5MI/dmdgCs3l6hoHrcUv41sRKew3jIwrp4qQDXiK99Utww==}
    engines: {node: '>= 0.8'}

  on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==}
    engines: {node: '>= 0.8'}

  on-headers@1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==}
    engines: {node: '>= 0.8'}

  once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==}

  onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==}
    engines: {node: '>=6'}

  open@8.4.2:
    resolution: {integrity: sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==}
    engines: {node: '>=12'}

  openurl@1.1.1:
    resolution: {integrity: sha512-d/gTkTb1i1GKz5k3XE3XFV/PxQ1k45zDqGP2OA7YhgsaLoqm6qRvARAZOFer1fcXritWlGBRCu/UgeS4HAnXAA==}

  opn@5.3.0:
    resolution: {integrity: sha512-bYJHo/LOmoTd+pfiYhfZDnf9zekVJrY+cnS2a5F2x+w5ppvTqObojTP7WiFG+kVZs9Inw+qQ/lw7TroWwhdd2g==}
    engines: {node: '>=4'}

  ora@5.4.1:
    resolution: {integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==}
    engines: {node: '>=10'}

  os-tmpdir@1.0.2:
    resolution: {integrity: sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==}
    engines: {node: '>=0.10.0'}

  p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==}
    engines: {node: '>=6'}

  p-limit@4.0.0:
    resolution: {integrity: sha512-5b0R4txpzjPWVw/cXXUResoD4hb6U/x9BH08L7nw+GN1sezDzPdxeRvpc9c433fZhBan/wusjbCsqwqm4EIBIQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==}
    engines: {node: '>=8'}

  p-locate@6.0.0:
    resolution: {integrity: sha512-wPrq66Llhl7/4AGC6I+cqxT07LhXvWL08LNXz1fENOw0Ap4sRZZ/gZpTTJ5jpurzzzfS2W/Ge9BY3LgLjCShcw==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  p-map@4.0.0:
    resolution: {integrity: sha512-/bjOqmgETBYB5BoEeGVea8dmvHb2m9GLy1E9W43yeyfP6QQCZGFNa+XRceJEuDB6zqr+gKpIAmlLebMpykw/MQ==}
    engines: {node: '>=10'}

  p-retry@4.6.2:
    resolution: {integrity: sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==}
    engines: {node: '>=8'}

  p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==}
    engines: {node: '>=6'}

  pacote@15.2.0:
    resolution: {integrity: sha512-rJVZeIwHTUta23sIZgEIM62WYwbmGbThdbnkt81ravBplQv+HjyroqnLRNH2+sLJHcGZmLRmhPwACqhfTcOmnA==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    hasBin: true

  pako@1.0.11:
    resolution: {integrity: sha512-4hLB8Py4zZce5s4yd9XzopqwVv/yGNhV1Bl8NTmCq1763HeK2+EwVTv+leGeL13Dnh2wfbqowVPXCIO0z4taYw==}

  parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==}
    engines: {node: '>=6'}

  parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==}
    engines: {node: '>=8'}

  parse-node-version@1.0.1:
    resolution: {integrity: sha512-3YHlOa/JgH6Mnpr05jP9eDG254US9ek25LyIxZlDItp2iJtwyaXQb57lBYLdT3MowkUFYEV2XXNAYIPlESvJlA==}
    engines: {node: '>= 0.10'}

  parse5-html-rewriting-stream@7.0.0:
    resolution: {integrity: sha512-mazCyGWkmCRWDI15Zp+UiCqMp/0dgEmkZRvhlsqqKYr4SsVm/TvnSpD9fCvqCA2zoWJcfRym846ejWBBHRiYEg==}

  parse5-sax-parser@7.0.0:
    resolution: {integrity: sha512-5A+v2SNsq8T6/mG3ahcz8ZtQ0OUFTatxPbeidoMB7tkJSGDY3tdfl4MHovtLQHkEn5CGxijNWRQHhRQ6IRpXKg==}

  parse5@6.0.1:
    resolution: {integrity: sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==}

  parse5@7.1.2:
    resolution: {integrity: sha512-Czj1WaSVpaoj0wbhMzLmWD69anp2WH7FXMB9n1Sy8/ZFF9jolSQVMu1Ij5WIyGmcBmhk7EOndpO4mIpihVqAXw==}

  parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==}
    engines: {node: '>= 0.8'}

  path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==}
    engines: {node: '>=8'}

  path-exists@5.0.0:
    resolution: {integrity: sha512-RjhtfwJOxzcFmNOi6ltcbcu4Iu+FL3zEj83dk4kAS+fVpTxXLO1b38RvJgT/0QwvV/L3aY9TAnyv0EOqW4GoMQ==}
    engines: {node: ^12.20.0 || ^14.13.1 || >=16.0.0}

  path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==}
    engines: {node: '>=0.10.0'}

  path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==}
    engines: {node: '>=8'}

  path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==}

  path-scurry@1.10.1:
    resolution: {integrity: sha512-MkhCqzzBEpPvxxQ71Md0b1Kk51W01lrYvlMzSUaIzNsODdd7mqhiimSZlr+VegAz5Z6Vzt9Xg2ttE//XBhH3EQ==}
    engines: {node: '>=16 || 14 >=14.17'}

  path-to-regexp@0.1.7:
    resolution: {integrity: sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==}

  path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==}
    engines: {node: '>=8'}

  picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==}

  picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==}
    engines: {node: '>=8.6'}

  pify@4.0.1:
    resolution: {integrity: sha512-uB80kBFb/tfd68bVleG9T5GGsGPjJrLAUpR5PZIrhBnIaRTQRjqdJSsIKkOP6OAIFbj7GOrcudc5pNjZ+geV2g==}
    engines: {node: '>=6'}

  piscina@4.0.0:
    resolution: {integrity: sha512-641nAmJS4k4iqpNUqfggqUBUMmlw0ZoM5VZKdQkV2e970Inn3Tk9kroCc1wpsYLD07vCwpys5iY0d3xI/9WkTg==}

  pkg-dir@7.0.0:
    resolution: {integrity: sha512-Ie9z/WINcxxLp27BKOCHGde4ITq9UklYKDzVo1nhk5sqGEXU3FpkwP5GM2voTGJkGd9B3Otl+Q4uwSOeSUtOBA==}
    engines: {node: '>=14.16'}

  portscanner@2.2.0:
    resolution: {integrity: sha512-IFroCz/59Lqa2uBvzK3bKDbDDIEaAY8XJ1jFxcLWTqosrsc32//P4VuSB2vZXoHiHqOmx8B5L5hnKOxL/7FlPw==}
    engines: {node: '>=0.4', npm: '>=1.0.0'}

  positioning@2.0.1:
    resolution: {integrity: sha512-DsAgM42kV/ObuwlRpAzDTjH9E8fGKkMDJHWFX+kfNXSxh7UCCQxEmdjv/Ws5Ft1XDnt3JT8fIDYeKNSE2TbttA==}

  postcss-loader@7.3.3:
    resolution: {integrity: sha512-YgO/yhtevGO/vJePCQmTxiaEwER94LABZN0ZMT4A0vsak9TpO+RvKRs7EmJ8peIlB9xfXCsS7M8LjqncsUZ5HA==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^5.0.0

  postcss-modules-extract-imports@3.0.0:
    resolution: {integrity: sha512-bdHleFnP3kZ4NYDhuGlVK+CMrQ/pqUm8bx/oGL93K6gVwiclvX5x0n76fYMKuIGKzlABOy13zsvqjb0f92TEXw==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-local-by-default@4.0.3:
    resolution: {integrity: sha512-2/u2zraspoACtrbFRnTijMiQtb4GW4BvatjaG/bCjYQo8kLTdevCUlwuBHx2sCnSyrI3x3qj4ZK1j5LQBgzmwA==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-scope@3.1.0:
    resolution: {integrity: sha512-SaIbK8XW+MZbd0xHPf7kdfA/3eOt7vxJ72IRecn3EzuZVLr1r0orzf0MX/pN8m+NMDoo6X/SQd8oeKqGZd8PXg==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-modules-values@4.0.0:
    resolution: {integrity: sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==}
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0

  postcss-selector-parser@6.0.14:
    resolution: {integrity: sha512-65xXYsT40i9GyWzlHQ5ShZoK7JZdySeOozi/tz2EezDo6c04q6+ckYMeoY7idaie1qp2dT5KoYQ2yky6JuoHnA==}
    engines: {node: '>=4'}

  postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==}

  postcss@8.4.31:
    resolution: {integrity: sha512-PS08Iboia9mts/2ygV3eLpY5ghnUcfLV/EXTOW1E2qYxJKGGBUtNjN76FYHnMs36RmARn41bC0AZmn+rR0OVpQ==}
    engines: {node: ^10 || ^12 || >=14}

  pretty-bytes@5.6.0:
    resolution: {integrity: sha512-FFw039TmrBqFK8ma/7OL3sDz/VytdtJr044/QUJtH0wK9lb9jLq9tJyIxUwtQJHwar2BqtiA4iCWSwo9JLkzFg==}
    engines: {node: '>=6'}

  proc-log@3.0.0:
    resolution: {integrity: sha512-++Vn7NS4Xf9NacaU9Xq3URUuqZETPsf8L4j5/ckhaRYsfPeRyzGw+iDjFhV/Jr3uNmTvvddEJFWh5R1gRgUH8A==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==}

  promise-inflight@1.0.1:
    resolution: {integrity: sha512-6zWPyEOFaQBJYcGMHBKTKJ3u6TBsnMFOIZSa6ce1e/ZrrsOlnHRHbabMjLiBYKp+n44X9eUI6VUPaukCXHuG4g==}
    peerDependencies:
      bluebird: '*'
    peerDependenciesMeta:
      bluebird:
        optional: true

  promise-retry@2.0.1:
    resolution: {integrity: sha512-y+WKFlBR8BGXnsNlIHFGPZmyDf3DFMoLhaflAnyZgV6rG6xu+JwesTo2Q9R6XwYmtmwAFCkAk3e35jEdoeh/3g==}
    engines: {node: '>=10'}

  proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==}
    engines: {node: '>= 0.10'}

  prr@1.0.1:
    resolution: {integrity: sha512-yPw4Sng1gWghHQWj0B3ZggWUm4qVbPwPFcRG8KyxiU7J2OHFSoEHKS+EZ3fv5l1t9CyCiop6l/ZYeWbrgoQejw==}

  psl@1.9.0:
    resolution: {integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==}

  punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==}
    engines: {node: '>=6'}

  qjobs@1.2.0:
    resolution: {integrity: sha512-8YOJEHtxpySA3fFDyCRxA+UUV+fA+rTWnuWvylOK/NCjhY+b4ocCtmu8TtsWb+mYeU+GCHf/S66KZF/AsteKHg==}
    engines: {node: '>=0.9'}

  qs@6.11.0:
    resolution: {integrity: sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==}
    engines: {node: '>=0.6'}

  querystringify@2.2.0:
    resolution: {integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==}

  queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==}

  randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==}

  range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==}
    engines: {node: '>= 0.6'}

  raw-body@2.5.1:
    resolution: {integrity: sha512-qqJBtEyVgS0ZmPGdCFPWJ3FreoqvG4MVQln/kCgF7Olq95IbOp0/BWyMwbdtn4VTvkM8Y7khCQ2Xgk/tcrCXig==}
    engines: {node: '>= 0.8'}

  raw-body@2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==}
    engines: {node: '>= 0.8'}

  read-package-json-fast@3.0.2:
    resolution: {integrity: sha512-0J+Msgym3vrLOUB3hzQCuZHII0xkNGCtz/HJH9xZshwv9DbDwkw1KaE3gx/e2J5rpEY5rtOy6cyhKOPrkP7FZw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  read-package-json@6.0.4:
    resolution: {integrity: sha512-AEtWXYfopBj2z5N5PbkAOeNHRPUg5q+Nen7QLxV8M2zJq1ym6/lCz3fYNTCXe19puu2d06jfHhrP7v/S2PtMMw==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    deprecated: This package is no longer supported. Please use @npmcli/package-json instead.

  readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==}

  readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==}
    engines: {node: '>= 6'}

  readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==}
    engines: {node: '>=8.10.0'}

  reflect-metadata@0.1.14:
    resolution: {integrity: sha512-ZhYeb6nRaXCfhnndflDK8qI6ZQ/YcWZCISRAWICW9XYqMUwjZM9Z0DveWX/ABN01oxSHwVxKQmxeYZSsm0jh5A==}

  regenerate-unicode-properties@10.1.1:
    resolution: {integrity: sha512-X007RyZLsCJVVrjgEFVpLUTZwyOZk3oiL75ZcuYjlIWd6rNJtOjkBwQc5AsRrpbKVkxN6sklw/k/9m2jJYOf8Q==}
    engines: {node: '>=4'}

  regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==}

  regenerator-runtime@0.13.11:
    resolution: {integrity: sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==}

  regenerator-transform@0.15.2:
    resolution: {integrity: sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==}

  regex-parser@2.2.11:
    resolution: {integrity: sha512-jbD/FT0+9MBU2XAZluI7w2OBs1RBi6p9M83nkoZayQXXU9e8Robt69FcZc7wU4eJD/YFTjn1JdCk3rbMJajz8Q==}

  regexpu-core@5.3.2:
    resolution: {integrity: sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==}
    engines: {node: '>=4'}

  regjsparser@0.9.1:
    resolution: {integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==}
    hasBin: true

  require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==}
    engines: {node: '>=0.10.0'}

  require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==}
    engines: {node: '>=0.10.0'}

  requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==}

  resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==}
    engines: {node: '>=4'}

  resolve-from@5.0.0:
    resolution: {integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==}
    engines: {node: '>=8'}

  resolve-url-loader@5.0.0:
    resolution: {integrity: sha512-uZtduh8/8srhBoMx//5bwqjQ+rfYOUq8zC9NrMUGtjBiGTtFJM42s58/36+hTqeqINcnYe08Nj3LkK9lW4N8Xg==}
    engines: {node: '>=12'}

  resolve@1.22.2:
    resolution: {integrity: sha512-Sb+mjNHOULsBv818T40qSPeRiuWLyaGMa5ewydRLFimneixmVy2zdivRl+AF6jaYPC8ERxGDmFSiqui6SfPd+g==}
    hasBin: true

  resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==}
    hasBin: true

  resp-modifier@6.0.2:
    resolution: {integrity: sha512-U1+0kWC/+4ncRFYqQWTx/3qkfE6a4B/h3XXgmXypfa0SPZ3t7cbbaFk297PjQS/yov24R18h6OZe6iZwj3NSLw==}
    engines: {node: '>= 0.8.0'}

  restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==}
    engines: {node: '>=8'}

  retry@0.12.0:
    resolution: {integrity: sha512-9LkiTwjUh6rT555DtE9rTX+BKByPfrMzEAtnlEtdEwr3Nkffwiihqe2bWADg+OQRjt9gl6ICdmB/ZFDCGAtSow==}
    engines: {node: '>= 4'}

  retry@0.13.1:
    resolution: {integrity: sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==}
    engines: {node: '>= 4'}

  reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==}
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}

  rfdc@1.3.0:
    resolution: {integrity: sha512-V2hovdzFbOi77/WajaSMXk2OLm+xNIeQdMMuB7icj7bk6zi2F8GGAxigcnDFpJHbNyNcgyJDiP+8nOrY5cZGrA==}

  rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==}
    hasBin: true

  rollup@3.29.4:
    resolution: {integrity: sha512-oWzmBZwvYrU0iJHtDmhsm662rC15FRXmcjCk1xD771dFDx5jJ02ufAQQTn0etB2emNk4J9EZg/yWKpsn9BWGRw==}
    engines: {node: '>=14.18.0', npm: '>=8.0.0'}
    hasBin: true

  rrweb-cssom@0.6.0:
    resolution: {integrity: sha512-APM0Gt1KoXBz0iIkkdB/kfvGOwC4UuJFeG/c+yV7wSc7q96cG/kJ0HiYCnzivD9SB53cLV1MlHFNfOuPaadYSw==}

  run-async@2.4.1:
    resolution: {integrity: sha512-tvVnVv01b8c1RrA6Ep7JkStj85Guv/YrMcwqYQnwjsAS2cTmmPGBBjAjpCW7RrSodNSoE2/qg9O4bceNvUuDgQ==}
    engines: {node: '>=0.12.0'}

  run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==}

  rx@4.1.0:
    resolution: {integrity: sha512-CiaiuN6gapkdl+cZUr67W6I8jquN4lkak3vtIsIWCl4XIPP8ffsoyN6/+PuGXnQy8Cu8W2y9Xxh31Rq4M6wUug==}

  rxjs@7.8.1:
    resolution: {integrity: sha512-AA3TVj+0A2iuIoQkWEK/tqFjBq2j+6PO6Y0zJcvzLAFhEFIO3HL0vls9hWLncZbAAbK0mar7oZ4V079I/qPMxg==}

  safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==}

  safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==}

  safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==}

  safevalues@0.3.4:
    resolution: {integrity: sha512-LRneZZRXNgjzwG4bDQdOTSbze3fHm1EAKN/8bePxnlEZiBmkYEDggaHbuvHI9/hoqHbGfsEA7tWS9GhYHZBBsw==}

  sass-loader@13.3.2:
    resolution: {integrity: sha512-CQbKl57kdEv+KDLquhC+gE3pXt74LEAzm+tzywcA0/aHZuub8wTErbjAoNI57rPUWRYRNC5WUnNl8eGJNbDdwg==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      fibers: '>= 3.1.0'
      node-sass: ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0
      sass: ^1.3.0
      sass-embedded: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      fibers:
        optional: true
      node-sass:
        optional: true
      sass:
        optional: true
      sass-embedded:
        optional: true

  sass@1.64.1:
    resolution: {integrity: sha512-16rRACSOFEE8VN7SCgBu1MpYCyN7urj9At898tyzdXFhC+a+yOX5dXwAR7L8/IdPJ1NB8OYoXmD55DM30B2kEQ==}
    engines: {node: '>=14.0.0'}
    hasBin: true

  sax@1.3.0:
    resolution: {integrity: sha512-0s+oAmw9zLl1V1cS9BtZN7JAd0cW5e0QH4W3LWEK6a4LaLEA2OTpGYWDY+6XasBLtz6wkm3u1xRw95mRuJ59WA==}

  saxes@5.0.1:
    resolution: {integrity: sha512-5LBh1Tls8c9xgGjw3QrMwETmTMVk0oFgvrFSvWx62llR2hcEInrKNZ2GZCCuuy2lvWrdl5jhbpeqc5hRYKFOcw==}
    engines: {node: '>=10'}

  saxes@6.0.0:
    resolution: {integrity: sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==}
    engines: {node: '>=v12.22.7'}

  schema-utils@3.3.0:
    resolution: {integrity: sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==}
    engines: {node: '>= 10.13.0'}

  schema-utils@4.2.0:
    resolution: {integrity: sha512-L0jRsrPpjdckP3oPug3/VxNKt2trR8TcabrM6FOAAlvC/9Phcmm+cuAgTlxBqdBR1WJx7Naj9WHw+aOmheSVbw==}
    engines: {node: '>= 12.13.0'}

  select-hose@2.0.0:
    resolution: {integrity: sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg==}

  selfsigned@2.4.1:
    resolution: {integrity: sha512-th5B4L2U+eGLq1TVh7zNRGBapioSORUeymIydxgFpwww9d2qyKvtuPU2jJuHvYAwwqi2Y596QBL3eEqcPEYL8Q==}
    engines: {node: '>=10'}

  semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==}
    hasBin: true

  semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==}
    hasBin: true

  semver@7.5.4:
    resolution: {integrity: sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==}
    engines: {node: '>=10'}
    hasBin: true

  send@0.16.2:
    resolution: {integrity: sha512-E64YFPUssFHEFBvpbbjr44NCLtI1AohxQ8ZSiJjQLskAdKuriYEP6VyGEsRDH8ScozGpkaX1BGvhanqCwkcEZw==}
    engines: {node: '>= 0.8.0'}

  send@0.18.0:
    resolution: {integrity: sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==}
    engines: {node: '>= 0.8.0'}

  serialize-javascript@6.0.1:
    resolution: {integrity: sha512-owoXEFjWRllis8/M1Q+Cw5k8ZH40e3zhp/ovX+Xr/vi1qj6QesbyXXViFbpNvWvPNAD62SutwEXavefrLJWj7w==}

  serve-index@1.9.1:
    resolution: {integrity: sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==}
    engines: {node: '>= 0.8.0'}

  serve-static@1.13.2:
    resolution: {integrity: sha512-p/tdJrO4U387R9oMjb1oj7qSMaMfmOyd4j9hOFoxZe2baQszgHcSWjuya/CiT5kgZZKRudHNOA0pYXOl8rQ5nw==}
    engines: {node: '>= 0.8.0'}

  serve-static@1.15.0:
    resolution: {integrity: sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==}
    engines: {node: '>= 0.8.0'}

  server-destroy@1.0.1:
    resolution: {integrity: sha512-rb+9B5YBIEzYcD6x2VKidaa+cqYBJQKnU4oe4E3ANwRRN56yk/ua1YCJT1n21NTS8w6CcOclAKNP3PhdCXKYtQ==}

  set-blocking@2.0.0:
    resolution: {integrity: sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==}

  set-function-length@1.1.1:
    resolution: {integrity: sha512-VoaqjbBJKiWtg4yRcKBQ7g7wnGnLV3M8oLvVWwOk2PdYY6PEFegR1vezXR0tw6fZGF9csVakIRjrJiy2veSBFQ==}
    engines: {node: '>= 0.4'}

  setprototypeof@1.1.0:
    resolution: {integrity: sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==}

  setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==}

  shallow-clone@3.0.1:
    resolution: {integrity: sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==}
    engines: {node: '>=8'}

  shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==}
    engines: {node: '>=8'}

  shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==}
    engines: {node: '>=8'}

  shell-quote@1.8.1:
    resolution: {integrity: sha512-6j1W9l1iAs/4xYBI1SYOVZyFcCis9b4KCLQ8fgAGG07QvzaRLVVRQvAy85yNmmZSjYjg4MWh4gNvlPujU/5LpA==}

  side-channel@1.0.4:
    resolution: {integrity: sha512-q5XPytqFEIKHkGdiMIrY10mvLRvnQh42/+GoBlFW3b2LXLE2xxJpZFdm94we0BaoV3RwJyGqg5wS7epxTv0Zvw==}

  signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==}

  signal-exit@4.1.0:
    resolution: {integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==}
    engines: {node: '>=14'}

  sigstore@1.9.0:
    resolution: {integrity: sha512-0Zjz0oe37d08VeOtBIuB6cRriqXse2e8w+7yIy2XSXjshRKxbc2KkhXjL229jXSxEm7UbcjS76wcJDGQddVI9A==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    hasBin: true

  slash@4.0.0:
    resolution: {integrity: sha512-3dOsAHXXUkQTpOYcoAxLIorMTp4gIQr5IW3iVb7A7lFIp0VHhnynm9izx6TssdrIcVIESAlVjtnO2K8bg+Coew==}
    engines: {node: '>=12'}

  smart-buffer@4.2.0:
    resolution: {integrity: sha512-94hK0Hh8rPqQl2xXc3HsaBoOXKV20MToPkcXvwbISWLEs+64sBq5kFgn2kJDHb1Pry9yrP0dxrCI9RRci7RXKg==}
    engines: {node: '>= 6.0.0', npm: '>= 3.0.0'}

  socket.io-adapter@2.5.2:
    resolution: {integrity: sha512-87C3LO/NOMc+eMcpcxUBebGjkpMDkNBS9tf7KJqcDsmL936EChtVva71Dw2q4tQcuVC+hAUy4an2NO/sYXmwRA==}

  socket.io-client@4.7.5:
    resolution: {integrity: sha512-sJ/tqHOCe7Z50JCBCXrsY3I2k03iOiUe+tj1OmKeD2lXPiGH/RUCdTZFoqVyN7l1MnpIzPrGtLcijffmeouNlQ==}
    engines: {node: '>=10.0.0'}

  socket.io-parser@4.2.4:
    resolution: {integrity: sha512-/GbIKmo8ioc+NIWIhwdecY0ge+qVBSMdgxGygevmdHj24bsfgtCmcUUcQ5ZzcylGFHsN3k4HB4Cgkl96KVnuew==}
    engines: {node: '>=10.0.0'}

  socket.io@4.7.2:
    resolution: {integrity: sha512-bvKVS29/I5fl2FGLNHuXlQaUH/BlzX1IN6S+NKLNZpBsPZIDH+90eQmCs2Railn4YUiww4SzUedJ6+uzwFnKLw==}
    engines: {node: '>=10.2.0'}

  sockjs@0.3.24:
    resolution: {integrity: sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==}

  socks-proxy-agent@7.0.0:
    resolution: {integrity: sha512-Fgl0YPZ902wEsAyiQ+idGd1A7rSFx/ayC1CQVMw5P+EQx2V0SgpGtf6OKFhVjPflPUl9YMmEOnmfjCdMUsygww==}
    engines: {node: '>= 10'}

  socks@2.7.1:
    resolution: {integrity: sha512-7maUZy1N7uo6+WVEX6psASxtNlKaNVMlGQKkG/63nEDdLOWNbiUMoLK7X4uYoLhQstau72mLgfEWcXcwsaHbYQ==}
    engines: {node: '>= 10.13.0', npm: '>= 3.0.0'}

  source-map-js@1.0.2:
    resolution: {integrity: sha512-R0XvVJ9WusLiqTCEiGCmICCMplcCkIwwR11mOSD9CR5u+IXYdiseeEuXCVAjS54zqwkLcPNnmU4OeJ6tUrWhDw==}
    engines: {node: '>=0.10.0'}

  source-map-loader@4.0.1:
    resolution: {integrity: sha512-oqXpzDIByKONVY8g1NUPOTQhe0UTU5bWUl32GSkqK2LjJj0HmwTMVKxcUip0RgAYhY1mqgOxjbQM48a0mmeNfA==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      webpack: ^5.72.1

  source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==}

  source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==}
    engines: {node: '>=0.10.0'}

  source-map@0.7.4:
    resolution: {integrity: sha512-l3BikUxvPOcn5E74dZiq5BGsTb5yEwhaTSzccU6t4sDOH8NWJCstKO5QT2CvtFoK6F0saL7p9xHAqHOlCPJygA==}
    engines: {node: '>= 8'}

  spdx-correct@3.2.0:
    resolution: {integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==}

  spdx-exceptions@2.3.0:
    resolution: {integrity: sha512-/tTrYOC7PPI1nUAgx34hUpqXuyJG+DTHJTnIULG4rDygi4xu/tfgmq1e1cIRwRzwZgo4NLySi+ricLkZkw4i5A==}

  spdx-expression-parse@3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==}

  spdx-license-ids@3.0.16:
    resolution: {integrity: sha512-eWN+LnM3GR6gPu35WxNgbGl8rmY1AEmoMDvL/QD6zYmPWgywxWqJWNdLGT+ke8dKNWrcYgYjPpG5gbTfghP8rw==}

  spdy-transport@3.0.0:
    resolution: {integrity: sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==}

  spdy@4.0.2:
    resolution: {integrity: sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==}
    engines: {node: '>=6.0.0'}

  sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==}

  ssri@10.0.5:
    resolution: {integrity: sha512-bSf16tAFkGeRlUNDjXu8FzaMQt6g2HZJrun7mtMbIPOddxt3GLMSz5VWUWcqTJUPfLEaDIepGxv+bYQW49596A==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  ssri@9.0.1:
    resolution: {integrity: sha512-o57Wcn66jMQvfHG1FlYbWeZWW/dHZhJXjpIcTfXldXEk5nz5lStPo3mK0OJQfGR3RbZUlbISexbljkJzuEj/8Q==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  statuses@1.3.1:
    resolution: {integrity: sha512-wuTCPGlJONk/a1kqZ4fQM2+908lC7fa7nPYpTC1EhnvqLX/IICbeP1OZGDtA374trpSq68YubKUMo8oRhN46yg==}
    engines: {node: '>= 0.6'}

  statuses@1.4.0:
    resolution: {integrity: sha512-zhSCtt8v2NDrRlPQpCNtw/heZLtfUDqxBM1udqikb/Hbk52LK4nQSwr10u77iopCW5LsyHpuXS0GnEc48mLeew==}
    engines: {node: '>= 0.6'}

  statuses@1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==}
    engines: {node: '>= 0.6'}

  statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==}
    engines: {node: '>= 0.8'}

  stream-throttle@0.1.3:
    resolution: {integrity: sha512-889+B9vN9dq7/vLbGyuHeZ6/ctf5sNuGWsDy89uNxkFTAgzy0eK7+w5fL3KLNRTkLle7EgZGvHUphZW0Q26MnQ==}
    engines: {node: '>= 0.10.0'}
    hasBin: true

  streamroller@3.1.5:
    resolution: {integrity: sha512-KFxaM7XT+irxvdqSP1LGLgNWbYN7ay5owZ3r/8t77p+EtSUAfUgtl7be3xtqtOmGUl9K9YPO2ca8133RlTjvKw==}
    engines: {node: '>=8.0'}

  string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==}
    engines: {node: '>=8'}

  string-width@5.1.2:
    resolution: {integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==}
    engines: {node: '>=12'}

  string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==}

  string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==}

  strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==}
    engines: {node: '>=8'}

  strip-ansi@7.1.0:
    resolution: {integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==}
    engines: {node: '>=12'}

  strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==}
    engines: {node: '>=6'}

  supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==}
    engines: {node: '>=4'}

  supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==}
    engines: {node: '>=8'}

  supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==}
    engines: {node: '>=10'}

  supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==}
    engines: {node: '>= 0.4'}

  symbol-observable@4.0.0:
    resolution: {integrity: sha512-b19dMThMV4HVFynSAM1++gBHAbk2Tc/osgLIBZMKsyqh34jb2e8Os7T6ZW/Bt3pJFdBTd2JwAnAAEQV7rSNvcQ==}
    engines: {node: '>=0.10'}

  symbol-tree@3.2.4:
    resolution: {integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==}

  tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==}
    engines: {node: '>=6'}

  tar@6.2.0:
    resolution: {integrity: sha512-/Wo7DcT0u5HUV486xg675HtjNd3BXZ6xDbzsCUZPt5iw8bTQ63bP0Raut3mvro9u+CUyq7YQd8Cx55fsZXxqLQ==}
    engines: {node: '>=10'}

  terser-webpack-plugin@5.3.9:
    resolution: {integrity: sha512-ZuXsqE07EcggTWQjXUj+Aot/OMcD0bMKGgF63f7UxYcu5/AJF53aIpK1YoP5xR9l6s/Hy2b+t1AM0bLNPRuhwA==}
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true

  terser@5.19.2:
    resolution: {integrity: sha512-qC5+dmecKJA4cpYxRa5aVkKehYsQKc+AHeKl0Oe62aYjBL8ZA33tTljktDHJSaxxMnbI5ZYw+o/S2DxxLu8OfA==}
    engines: {node: '>=10'}
    hasBin: true

  test-exclude@6.0.0:
    resolution: {integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==}
    engines: {node: '>=8'}

  text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==}

  through@2.3.8:
    resolution: {integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==}

  thunky@1.1.0:
    resolution: {integrity: sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==}

  tmp@0.0.33:
    resolution: {integrity: sha512-jRCJlojKnZ3addtTOjdIqoRuPEKBvNXcGYqzO6zWZX8KfKEpnGY5jfggJQ3EjKuu8D4bJRr0y+cYJFmYbImXGw==}
    engines: {node: '>=0.6.0'}

  tmp@0.2.1:
    resolution: {integrity: sha512-76SUhtfqR2Ijn+xllcI5P1oyannHNHByD80W1q447gU3mp9G9PSpGdWmjUOHRDPiHYacIk66W7ubDTuPF3BEtQ==}
    engines: {node: '>=8.17.0'}

  to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==}
    engines: {node: '>=4'}

  to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==}
    engines: {node: '>=8.0'}

  toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==}
    engines: {node: '>=0.6'}

  tough-cookie@4.1.3:
    resolution: {integrity: sha512-aX/y5pVRkfRnfmuX+OdbSdXvPe6ieKX/G2s7e98f4poJHnqH3281gDPm/metm6E/WRamfx7WC4HUqkWHfQHprw==}
    engines: {node: '>=6'}

  tr46@2.1.0:
    resolution: {integrity: sha512-15Ih7phfcdP5YxqiB+iDtLoaTz4Nd35+IiAv0kQ5FNKHzXgdWqPoTIqEDDJmXceQt4JZk6lVPT8lnDlPpGDppw==}
    engines: {node: '>=8'}

  tr46@4.1.1:
    resolution: {integrity: sha512-2lv/66T7e5yNyhAAC4NaKe5nVavzuGJQVVtRYLyQ2OI8tsJ61PMLlelehb0wi2Hx6+hT/OJUWZcw8MjlSRnxvw==}
    engines: {node: '>=14'}

  tree-kill@1.2.2:
    resolution: {integrity: sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==}
    hasBin: true

  tslib@2.6.1:
    resolution: {integrity: sha512-t0hLfiEKfMUoqhG+U1oid7Pva4bbDPHYfJNiB7BiIjRkj1pyC++4N3huJfqY6aRH6VTB0rvtzQwjM4K6qpfOig==}

  tslib@2.6.2:
    resolution: {integrity: sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==}

  tuf-js@1.1.7:
    resolution: {integrity: sha512-i3P9Kgw3ytjELUfpuKVDNBJvk4u5bXL6gskv572mcevPbSKCV3zt3djhmlEQ65yERjIbOSncy7U4cQJaB1CBCg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  type-fest@0.21.3:
    resolution: {integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==}
    engines: {node: '>=10'}

  type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==}
    engines: {node: '>= 0.6'}

  typed-assert@1.0.9:
    resolution: {integrity: sha512-KNNZtayBCtmnNmbo5mG47p1XsCyrx6iVqomjcZnec/1Y5GGARaxPs6r49RnSPeUP3YjNYiU9sQHAtY4BBvnZwg==}

  typescript@5.1.6:
    resolution: {integrity: sha512-zaWCozRZ6DLEWAWFrVDz1H6FVXzUSfTy5FUMWsQlU8Ym5JP9eO4xkTIROFCQvhQf61z6O/G6ugw3SgAnvvm+HA==}
    engines: {node: '>=14.17'}
    hasBin: true

  ua-parser-js@0.7.37:
    resolution: {integrity: sha512-xV8kqRKM+jhMvcHWUKthV9fNebIzrNy//2O9ZwWcfiBFR5f25XVZPLlEajk/sf3Ra15V92isyQqnIEXRDaZWEA==}

  ua-parser-js@1.0.37:
    resolution: {integrity: sha512-bhTyI94tZofjo+Dn8SN6Zv8nBDvyXTymAdM3LDI/0IboIUwTu1rEhW7v2TfiVsoYWgkQ4kOVqnI8APUFbIQIFQ==}

  unicode-canonical-property-names-ecmascript@2.0.0:
    resolution: {integrity: sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==}
    engines: {node: '>=4'}

  unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==}
    engines: {node: '>=4'}

  unicode-match-property-value-ecmascript@2.1.0:
    resolution: {integrity: sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA==}
    engines: {node: '>=4'}

  unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==}
    engines: {node: '>=4'}

  unique-filename@2.0.1:
    resolution: {integrity: sha512-ODWHtkkdx3IAR+veKxFV+VBkUMcN+FaqzUUd7IZzt+0zhDZFPFxhlqwPF3YQvMHx1TD0tdgYl+kuPnJ8E6ql7A==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  unique-filename@3.0.0:
    resolution: {integrity: sha512-afXhuC55wkAmZ0P18QsVE6kp8JaxrEokN2HGIoIVv2ijHQd419H0+6EigAFcIzXeMIkcIkNBpB3L/DXB3cTS/g==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  unique-slug@3.0.0:
    resolution: {integrity: sha512-8EyMynh679x/0gqE9fT9oilG+qEt+ibFyqjuVTsZn1+CMxH+XLlpvr2UZx4nVcCwTpx81nICr2JQFkM+HPLq4w==}
    engines: {node: ^12.13.0 || ^14.15.0 || >=16.0.0}

  unique-slug@4.0.0:
    resolution: {integrity: sha512-WrcA6AyEfqDX5bWige/4NQfPZMtASNVxdmWR76WESYQVAACSgWcR6e9i0mofqqBxYFtL4oAxPIptY73/0YE1DQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  universalify@0.1.2:
    resolution: {integrity: sha512-rBJeI5CXAlmy1pV+617WB9J63U6XcazHHF2f2dbJix4XzpUF0RS3Zbj0FGIOCAva5P/d/GBOYaACQ1w+0azUkg==}
    engines: {node: '>= 4.0.0'}

  universalify@0.2.0:
    resolution: {integrity: sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==}
    engines: {node: '>= 4.0.0'}

  unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==}
    engines: {node: '>= 0.8'}

  update-browserslist-db@1.0.13:
    resolution: {integrity: sha512-xebP81SNcPuNpPP3uzeW1NYXxI3rxyJzF3pD6sH4jE7o/IX+WtSpwnVU+qIsDPyk0d3hmFQ7mjqc6AtV604hbg==}
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'

  uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==}

  url-parse@1.5.10:
    resolution: {integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==}

  util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==}

  utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==}
    engines: {node: '>= 0.4.0'}

  uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==}
    hasBin: true

  validate-npm-package-license@3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==}

  validate-npm-package-name@5.0.0:
    resolution: {integrity: sha512-YuKoXDAhBYxY7SfOKxHBDoSyENFeW5VvIIQp2TGQuit8gpK6MnWaQelBKxso72DoxTZfZdcP3W90LqpSkgPzLQ==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}

  vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==}
    engines: {node: '>= 0.8'}

  vite@4.5.3:
    resolution: {integrity: sha512-kQL23kMeX92v3ph7IauVkXkikdDRsYMGTVl5KY2E9OY4ONLvkHf04MDTbnfo6NKxZiDLWzVpP5oTa8hQD8U3dg==}
    engines: {node: ^14.18.0 || >=16.0.0}
    hasBin: true
    peerDependencies:
      '@types/node': '>= 14'
      less: '*'
      lightningcss: ^1.21.0
      sass: '*'
      stylus: '*'
      sugarss: '*'
      terser: ^5.4.0
    peerDependenciesMeta:
      '@types/node':
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  void-elements@2.0.1:
    resolution: {integrity: sha512-qZKX4RnBzH2ugr8Lxa7x+0V6XD9Sb/ouARtiasEQCHB1EVU4NXtmHsDDrx1dO4ne5fc3J6EW05BP1Dl0z0iung==}
    engines: {node: '>=0.10.0'}

  w3c-hr-time@1.0.2:
    resolution: {integrity: sha512-z8P5DvDNjKDoFIHK7q8r8lackT6l+jo/Ye3HOle7l9nICP9lf1Ci25fy9vHd0JOWewkIFzXIEig3TdKT7JQ5fQ==}
    deprecated: Use your platform's native performance.now() and performance.timeOrigin.

  w3c-xmlserializer@2.0.0:
    resolution: {integrity: sha512-4tzD0mF8iSiMiNs30BiLO3EpfGLZUT2MSX/G+o7ZywDzliWQ3OPtTZ0PTC3B3ca1UAf4cJMHB+2Bf56EriJuRA==}
    engines: {node: '>=10'}

  w3c-xmlserializer@4.0.0:
    resolution: {integrity: sha512-d+BFHzbiCx6zGfz0HyQ6Rg69w9k19nviJspaj4yNscGjrHu94sVP+aRm75yEbCh+r2/yR+7q6hux9LVtbuTGBw==}
    engines: {node: '>=14'}

  watchpack@2.4.0:
    resolution: {integrity: sha512-Lcvm7MGST/4fup+ifyKi2hjyIAwcdI4HRgtvTpIUxBRhB+RFtUh8XtDOxUfctVCnhVi+QQj49i91OyvzkJl6cg==}
    engines: {node: '>=10.13.0'}

  wbuf@1.7.3:
    resolution: {integrity: sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==}

  wcwidth@1.0.1:
    resolution: {integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==}

  webidl-conversions@5.0.0:
    resolution: {integrity: sha512-VlZwKPCkYKxQgeSbH5EyngOmRp7Ww7I9rQLERETtf5ofd9pGeswWiOtogpEO850jziPRarreGxn5QIiTqpb2wA==}
    engines: {node: '>=8'}

  webidl-conversions@6.1.0:
    resolution: {integrity: sha512-qBIvFLGiBpLjfwmYAaHPXsn+ho5xZnGvyGvsarywGNc8VyQJUMHJ8OBKGGrPER0okBeMDaan4mNBlgBROxuI8w==}
    engines: {node: '>=10.4'}

  webidl-conversions@7.0.0:
    resolution: {integrity: sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==}
    engines: {node: '>=12'}

  webpack-dev-middleware@5.3.3:
    resolution: {integrity: sha512-hj5CYrY0bZLB+eTO+x/j67Pkrquiy7kWepMHmUMoPsmcUaeEnQJqFzHJOyxgWlq746/wUuA64p9ta34Kyb01pA==}
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0

  webpack-dev-middleware@6.1.2:
    resolution: {integrity: sha512-Wu+EHmX326YPYUpQLKmKbTyZZJIB8/n6R09pTmB03kJmnMsVPTo9COzHZFr01txwaCAuZvfBJE4ZCHRcKs5JaQ==}
    engines: {node: '>= 14.15.0'}
    peerDependencies:
      webpack: ^5.0.0
    peerDependenciesMeta:
      webpack:
        optional: true

  webpack-dev-server@4.15.1:
    resolution: {integrity: sha512-5hbAst3h3C3L8w6W4P96L5vaV0PxSmJhxZvWKYIdgxOQm8pNZ5dEOmmSLBVpP85ReeyRt6AS1QJNyo/oFFPeVA==}
    engines: {node: '>= 12.13.0'}
    hasBin: true
    peerDependencies:
      webpack: ^4.37.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack:
        optional: true
      webpack-cli:
        optional: true

  webpack-merge@5.9.0:
    resolution: {integrity: sha512-6NbRQw4+Sy50vYNTw7EyOn41OZItPiXB8GNv3INSoe3PSFaHJEz3SHTrYVaRm2LilNGnFUzh0FAwqPEmU/CwDg==}
    engines: {node: '>=10.0.0'}

  webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==}
    engines: {node: '>=10.13.0'}

  webpack-subresource-integrity@5.1.0:
    resolution: {integrity: sha512-sacXoX+xd8r4WKsy9MvH/q/vBtEHr86cpImXwyg74pFIpERKt6FmB8cXpeuh0ZLgclOlHI4Wcll7+R5L02xk9Q==}
    engines: {node: '>= 12'}
    peerDependencies:
      html-webpack-plugin: '>= 5.0.0-beta.1 < 6'
      webpack: ^5.12.0
    peerDependenciesMeta:
      html-webpack-plugin:
        optional: true

  webpack@5.88.2:
    resolution: {integrity: sha512-JmcgNZ1iKj+aiR0OvTYtWQqJwq37Pf683dY9bVORwVbUrDhLhdn/PlO2sHsFHPkj7sHNQF3JwaAkp49V+Sq1tQ==}
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true

  websocket-driver@0.7.4:
    resolution: {integrity: sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==}
    engines: {node: '>=0.8.0'}

  websocket-extensions@0.1.4:
    resolution: {integrity: sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==}
    engines: {node: '>=0.8.0'}

  whatwg-encoding@1.0.5:
    resolution: {integrity: sha512-b5lim54JOPN9HtzvK9HFXvBma/rnfFeqsic0hSpjtDbVxR3dJKLc+KB4V6GgiGOvl7CY/KNh8rxSo9DKQrnUEw==}

  whatwg-encoding@2.0.0:
    resolution: {integrity: sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==}
    engines: {node: '>=12'}

  whatwg-mimetype@2.3.0:
    resolution: {integrity: sha512-M4yMwr6mAnQz76TbJm914+gPpB/nCwvZbJU28cUD6dR004SAxDLOOSUaB1JDRqLtaOV/vi0IC5lEAGFgrjGv/g==}

  whatwg-mimetype@3.0.0:
    resolution: {integrity: sha512-nt+N2dzIutVRxARx1nghPKGv1xHikU7HKdfafKkLNLindmPU/ch3U31NOCGGA/dmPcmb1VlofO0vnKAcsm0o/Q==}
    engines: {node: '>=12'}

  whatwg-url@12.0.1:
    resolution: {integrity: sha512-Ed/LrqB8EPlGxjS+TrsXcpUond1mhccS3pchLhzSgPCnTimUCKj3IZE75pAs5m6heB2U2TMerKFUXheyHY+VDQ==}
    engines: {node: '>=14'}

  whatwg-url@8.7.0:
    resolution: {integrity: sha512-gAojqb/m9Q8a5IV96E3fHJM70AzCkgt4uXYX2O7EmuyOnLrViCQlsEBmF9UQIu3/aeAIp2U17rtbpZWNntQqdg==}
    engines: {node: '>=10'}

  which@1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==}
    hasBin: true

  which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==}
    engines: {node: '>= 8'}
    hasBin: true

  which@3.0.1:
    resolution: {integrity: sha512-XA1b62dzQzLfaEOSQFTCOd5KFf/1VSzZo7/7TUjnya6u0vGGKzU96UQBZTAThCb2j4/xjBAyii1OhRLJEivHvg==}
    engines: {node: ^14.17.0 || ^16.13.0 || >=18.0.0}
    hasBin: true

  wide-align@1.1.5:
    resolution: {integrity: sha512-eDMORYaPNZ4sQIuuYPDHdQvf4gyCF9rEEV/yPxGfwPkRodwEgiMUUXTx/dex+Me0wxx53S+NgUHaP7y3MGlDmg==}

  wildcard@2.0.1:
    resolution: {integrity: sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==}

  wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==}
    engines: {node: '>=10'}

  wrap-ansi@8.1.0:
    resolution: {integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==}
    engines: {node: '>=12'}

  wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==}

  ws@7.5.9:
    resolution: {integrity: sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==}
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.11.0:
    resolution: {integrity: sha512-HPG3wQd9sNQoT9xHyNCXoDUa+Xw/VevmY9FoHyQ+g+rrMn4j6FB4np7Z0OhdTgjx6MgQLK7jwSy1YecU1+4Asg==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  ws@8.16.0:
    resolution: {integrity: sha512-HS0c//TP7Ina87TfiPUz1rQzMhHrl/SG2guqRcTOIUYD2q8uhUdNHZYJUaQ8aTGPzCh+c6oawMKW35nFl1dxyQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xhr2@0.2.1:
    resolution: {integrity: sha512-sID0rrVCqkVNUn8t6xuv9+6FViXjUVXq8H5rWOH2rz9fDNQEd4g0EA2XlcEdJXRz5BMEn4O1pJFdT+z4YHhoWw==}
    engines: {node: '>= 6'}

  xml-name-validator@3.0.0:
    resolution: {integrity: sha512-A5CUptxDsvxKJEU3yO6DuWBSJz/qizqzJKOMIfUJHETbBw/sFaDxgd6fxm1ewUaM0jZ444Fc5vC5ROYurg/4Pw==}

  xml-name-validator@4.0.0:
    resolution: {integrity: sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==}
    engines: {node: '>=12'}

  xmlchars@2.2.0:
    resolution: {integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==}

  xmlhttprequest-ssl@2.0.0:
    resolution: {integrity: sha512-QKxVRxiRACQcVuQEYFsI1hhkrMlrXHPegbbd1yn9UHOmRxY+si12nQYzri3vbzt8VdTTRviqcKxcyllFas5z2A==}
    engines: {node: '>=0.4.0'}

  y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==}
    engines: {node: '>=10'}

  yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==}

  yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==}

  yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==}
    engines: {node: '>=10'}

  yargs-parser@21.1.1:
    resolution: {integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==}
    engines: {node: '>=12'}

  yargs@16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==}
    engines: {node: '>=10'}

  yargs@17.1.1:
    resolution: {integrity: sha512-c2k48R0PwKIqKhPMWjeiF6y2xY/gPMUlro0sgxqXpbOIohWiLNXWslsootttv7E1e73QPAMQSg5FeySbVcpsPQ==}
    engines: {node: '>=12'}

  yargs@17.7.2:
    resolution: {integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==}
    engines: {node: '>=12'}

  yocto-queue@1.0.0:
    resolution: {integrity: sha512-9bnSc/HEW2uRy67wc+T8UwauLuPJVn28jb+GtJY16iiKWyvmYJRXVT4UamsAEGQfPohgr2q4Tq0sQbQlxTfi1g==}
    engines: {node: '>=12.20'}

  zone.js@0.13.3:
    resolution: {integrity: sha512-MKPbmZie6fASC/ps4dkmIhaT5eonHkEt6eAy80K42tAm0G2W+AahLJjbfi6X9NPdciOE9GRFTTM8u2IiF6O3ww==}

snapshots:

  '@ampproject/remapping@2.2.1':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.20

  '@angular-devkit/architect@0.1602.11(chokidar@3.5.3)':
    dependencies:
      '@angular-devkit/core': 16.2.11(chokidar@3.5.3)
      rxjs: 7.8.1
    transitivePeerDependencies:
      - chokidar

  '@angular-devkit/architect@0.1602.14(chokidar@3.5.3)':
    dependencies:
      '@angular-devkit/core': 16.2.14(chokidar@3.5.3)
      rxjs: 7.8.1
    transitivePeerDependencies:
      - chokidar

  '@angular-devkit/build-angular@16.2.14(@angular/compiler-cli@16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6))(@angular/platform-server@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))))(@types/node@16.18.97)(karma@6.4.2)(typescript@5.1.6)':
    dependencies:
      '@ampproject/remapping': 2.2.1
      '@angular-devkit/architect': 0.1602.14(chokidar@3.5.3)
      '@angular-devkit/build-webpack': 0.1602.14(chokidar@3.5.3)(webpack-dev-server@4.15.1(webpack@5.88.2(esbuild@0.18.17)))(webpack@5.88.2(esbuild@0.18.17))
      '@angular-devkit/core': 16.2.14(chokidar@3.5.3)
      '@angular/compiler-cli': 16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6)
      '@babel/core': 7.22.9
      '@babel/generator': 7.22.9
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/plugin-proposal-async-generator-functions': 7.20.7(@babel/core@7.22.9)
      '@babel/plugin-transform-async-to-generator': 7.22.5(@babel/core@7.22.9)
      '@babel/plugin-transform-runtime': 7.22.9(@babel/core@7.22.9)
      '@babel/preset-env': 7.22.9(@babel/core@7.22.9)
      '@babel/runtime': 7.22.6
      '@babel/template': 7.22.5
      '@discoveryjs/json-ext': 0.5.7
      '@ngtools/webpack': 16.2.14(@angular/compiler-cli@16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6))(typescript@5.1.6)(webpack@5.88.2(esbuild@0.18.17))
      '@vitejs/plugin-basic-ssl': 1.0.1(vite@4.5.3(@types/node@16.18.97)(less@4.1.3)(sass@1.64.1)(terser@5.19.2))
      ansi-colors: 4.1.3
      autoprefixer: 10.4.14(postcss@8.4.31)
      babel-loader: 9.1.3(@babel/core@7.22.9)(webpack@5.88.2(esbuild@0.18.17))
      babel-plugin-istanbul: 6.1.1
      browserslist: 4.22.2
      chokidar: 3.5.3
      copy-webpack-plugin: 11.0.0(webpack@5.88.2(esbuild@0.18.17))
      critters: 0.0.20
      css-loader: 6.8.1(webpack@5.88.2(esbuild@0.18.17))
      esbuild-wasm: 0.18.17
      fast-glob: 3.3.1
      guess-parser: 0.4.22(typescript@5.1.6)
      https-proxy-agent: 5.0.1
      inquirer: 8.2.4
      jsonc-parser: 3.2.0
      karma-source-map-support: 1.4.0
      less: 4.1.3
      less-loader: 11.1.0(less@4.1.3)(webpack@5.88.2(esbuild@0.18.17))
      license-webpack-plugin: 4.0.2(webpack@5.88.2(esbuild@0.18.17))
      loader-utils: 3.2.1
      magic-string: 0.30.1
      mini-css-extract-plugin: 2.7.6(webpack@5.88.2(esbuild@0.18.17))
      mrmime: 1.0.1
      open: 8.4.2
      ora: 5.4.1
      parse5-html-rewriting-stream: 7.0.0
      picomatch: 2.3.1
      piscina: 4.0.0
      postcss: 8.4.31
      postcss-loader: 7.3.3(postcss@8.4.31)(typescript@5.1.6)(webpack@5.88.2(esbuild@0.18.17))
      resolve-url-loader: 5.0.0
      rxjs: 7.8.1
      sass: 1.64.1
      sass-loader: 13.3.2(sass@1.64.1)(webpack@5.88.2(esbuild@0.18.17))
      semver: 7.5.4
      source-map-loader: 4.0.1(webpack@5.88.2(esbuild@0.18.17))
      source-map-support: 0.5.21
      terser: 5.19.2
      text-table: 0.2.0
      tree-kill: 1.2.2
      tslib: 2.6.1
      typescript: 5.1.6
      vite: 4.5.3(@types/node@16.18.97)(less@4.1.3)(sass@1.64.1)(terser@5.19.2)
      webpack: 5.88.2(esbuild@0.18.17)
      webpack-dev-middleware: 6.1.2(webpack@5.88.2(esbuild@0.18.17))
      webpack-dev-server: 4.15.1(webpack@5.88.2(esbuild@0.18.17))
      webpack-merge: 5.9.0
      webpack-subresource-integrity: 5.1.0(webpack@5.88.2(esbuild@0.18.17))
    optionalDependencies:
      '@angular/platform-server': 16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))
      esbuild: 0.18.17
      karma: 6.4.2
    transitivePeerDependencies:
      - '@swc/core'
      - '@types/node'
      - bufferutil
      - canvas
      - debug
      - fibers
      - html-webpack-plugin
      - lightningcss
      - node-sass
      - sass-embedded
      - stylus
      - sugarss
      - supports-color
      - uglify-js
      - utf-8-validate
      - webpack-cli

  '@angular-devkit/build-webpack@0.1602.14(chokidar@3.5.3)(webpack-dev-server@4.15.1(webpack@5.88.2(esbuild@0.18.17)))(webpack@5.88.2(esbuild@0.18.17))':
    dependencies:
      '@angular-devkit/architect': 0.1602.14(chokidar@3.5.3)
      rxjs: 7.8.1
      webpack: 5.88.2(esbuild@0.18.17)
      webpack-dev-server: 4.15.1(webpack@5.88.2(esbuild@0.18.17))
    transitivePeerDependencies:
      - chokidar

  '@angular-devkit/core@16.2.11(chokidar@3.5.3)':
    dependencies:
      ajv: 8.12.0
      ajv-formats: 2.1.1(ajv@8.12.0)
      jsonc-parser: 3.2.0
      picomatch: 2.3.1
      rxjs: 7.8.1
      source-map: 0.7.4
    optionalDependencies:
      chokidar: 3.5.3

  '@angular-devkit/core@16.2.14(chokidar@3.5.3)':
    dependencies:
      ajv: 8.12.0
      ajv-formats: 2.1.1(ajv@8.12.0)
      jsonc-parser: 3.2.0
      picomatch: 2.3.1
      rxjs: 7.8.1
      source-map: 0.7.4
    optionalDependencies:
      chokidar: 3.5.3

  '@angular-devkit/schematics@16.2.14(chokidar@3.5.3)':
    dependencies:
      '@angular-devkit/core': 16.2.14(chokidar@3.5.3)
      jsonc-parser: 3.2.0
      magic-string: 0.30.1
      ora: 5.4.1
      rxjs: 7.8.1
    transitivePeerDependencies:
      - chokidar

  '@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))':
    dependencies:
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      tslib: 2.6.2

  '@angular/cdk@16.2.14(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)':
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      rxjs: 7.8.1
      tslib: 2.6.2
    optionalDependencies:
      parse5: 7.1.2

  '@angular/cli@16.2.14(chokidar@3.5.3)':
    dependencies:
      '@angular-devkit/architect': 0.1602.14(chokidar@3.5.3)
      '@angular-devkit/core': 16.2.14(chokidar@3.5.3)
      '@angular-devkit/schematics': 16.2.14(chokidar@3.5.3)
      '@schematics/angular': 16.2.14(chokidar@3.5.3)
      '@yarnpkg/lockfile': 1.1.0
      ansi-colors: 4.1.3
      ini: 4.1.1
      inquirer: 8.2.4
      jsonc-parser: 3.2.0
      npm-package-arg: 10.1.0
      npm-pick-manifest: 8.0.1
      open: 8.4.2
      ora: 5.4.1
      pacote: 15.2.0
      resolve: 1.22.2
      semver: 7.5.4
      symbol-observable: 4.0.0
      yargs: 17.7.2
    transitivePeerDependencies:
      - bluebird
      - chokidar
      - supports-color

  '@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)':
    dependencies:
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      rxjs: 7.8.1
      tslib: 2.6.2

  '@angular/compiler-cli@16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6)':
    dependencies:
      '@angular/compiler': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      '@babel/core': 7.23.2
      '@jridgewell/sourcemap-codec': 1.4.15
      chokidar: 3.5.3
      convert-source-map: 1.9.0
      reflect-metadata: 0.1.14
      semver: 7.5.4
      tslib: 2.6.2
      typescript: 5.1.6
      yargs: 17.7.2
    transitivePeerDependencies:
      - supports-color

  '@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))':
    dependencies:
      tslib: 2.6.2
    optionalDependencies:
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)

  '@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)':
    dependencies:
      rxjs: 7.8.1
      tslib: 2.6.2
      zone.js: 0.13.3

  '@angular/forms@16.2.12(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(rxjs@7.8.1)':
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@angular/platform-browser': 16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      rxjs: 7.8.1
      tslib: 2.6.2

  '@angular/material@16.2.14(dba6c3a62b3e5c2bbd117b00518804f3)':
    dependencies:
      '@angular/animations': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      '@angular/cdk': 16.2.14(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@angular/forms': 16.2.12(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(rxjs@7.8.1)
      '@angular/platform-browser': 16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/auto-init': 15.0.0-canary.bc9ae6c9c.0
      '@material/banner': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/button': 15.0.0-canary.bc9ae6c9c.0
      '@material/card': 15.0.0-canary.bc9ae6c9c.0
      '@material/checkbox': 15.0.0-canary.bc9ae6c9c.0
      '@material/chips': 15.0.0-canary.bc9ae6c9c.0
      '@material/circular-progress': 15.0.0-canary.bc9ae6c9c.0
      '@material/data-table': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dialog': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/drawer': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/fab': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/floating-label': 15.0.0-canary.bc9ae6c9c.0
      '@material/form-field': 15.0.0-canary.bc9ae6c9c.0
      '@material/icon-button': 15.0.0-canary.bc9ae6c9c.0
      '@material/image-list': 15.0.0-canary.bc9ae6c9c.0
      '@material/layout-grid': 15.0.0-canary.bc9ae6c9c.0
      '@material/line-ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/linear-progress': 15.0.0-canary.bc9ae6c9c.0
      '@material/list': 15.0.0-canary.bc9ae6c9c.0
      '@material/menu': 15.0.0-canary.bc9ae6c9c.0
      '@material/menu-surface': 15.0.0-canary.bc9ae6c9c.0
      '@material/notched-outline': 15.0.0-canary.bc9ae6c9c.0
      '@material/radio': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/segmented-button': 15.0.0-canary.bc9ae6c9c.0
      '@material/select': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/slider': 15.0.0-canary.bc9ae6c9c.0
      '@material/snackbar': 15.0.0-canary.bc9ae6c9c.0
      '@material/switch': 15.0.0-canary.bc9ae6c9c.0
      '@material/tab': 15.0.0-canary.bc9ae6c9c.0
      '@material/tab-bar': 15.0.0-canary.bc9ae6c9c.0
      '@material/tab-indicator': 15.0.0-canary.bc9ae6c9c.0
      '@material/tab-scroller': 15.0.0-canary.bc9ae6c9c.0
      '@material/textfield': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tooltip': 15.0.0-canary.bc9ae6c9c.0
      '@material/top-app-bar': 15.0.0-canary.bc9ae6c9c.0
      '@material/touch-target': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      rxjs: 7.8.1
      tslib: 2.6.2

  '@angular/platform-browser-dynamic@16.2.12(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))':
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/compiler': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@angular/platform-browser': 16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      tslib: 2.6.2

  '@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))':
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      tslib: 2.6.2
    optionalDependencies:
      '@angular/animations': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))

  '@angular/platform-server@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))':
    dependencies:
      '@angular/animations': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/compiler': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@angular/platform-browser': 16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      tslib: 2.6.2
      xhr2: 0.2.1

  '@angular/router@16.2.12(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(rxjs@7.8.1)':
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@angular/platform-browser': 16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      rxjs: 7.8.1
      tslib: 2.6.2

  '@assemblyscript/loader@0.10.1': {}

  '@babel/code-frame@7.23.5':
    dependencies:
      '@babel/highlight': 7.23.4
      chalk: 2.4.2

  '@babel/compat-data@7.23.5': {}

  '@babel/core@7.22.9':
    dependencies:
      '@ampproject/remapping': 2.2.1
      '@babel/code-frame': 7.23.5
      '@babel/generator': 7.23.6
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.22.9)
      '@babel/helpers': 7.23.6
      '@babel/parser': 7.23.6
      '@babel/template': 7.22.15
      '@babel/traverse': 7.23.6
      '@babel/types': 7.23.6
      convert-source-map: 1.9.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/core@7.23.2':
    dependencies:
      '@ampproject/remapping': 2.2.1
      '@babel/code-frame': 7.23.5
      '@babel/generator': 7.23.6
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.23.2)
      '@babel/helpers': 7.23.6
      '@babel/parser': 7.23.6
      '@babel/template': 7.22.15
      '@babel/traverse': 7.23.6
      '@babel/types': 7.23.6
      convert-source-map: 2.0.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/core@7.23.6':
    dependencies:
      '@ampproject/remapping': 2.2.1
      '@babel/code-frame': 7.23.5
      '@babel/generator': 7.23.6
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.23.6)
      '@babel/helpers': 7.23.6
      '@babel/parser': 7.23.6
      '@babel/template': 7.22.15
      '@babel/traverse': 7.23.6
      '@babel/types': 7.23.6
      convert-source-map: 2.0.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/generator@7.22.9':
    dependencies:
      '@babel/types': 7.23.6
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.20
      jsesc: 2.5.2

  '@babel/generator@7.23.6':
    dependencies:
      '@babel/types': 7.23.6
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.20
      jsesc: 2.5.2

  '@babel/helper-annotate-as-pure@7.22.5':
    dependencies:
      '@babel/types': 7.23.6

  '@babel/helper-builder-binary-assignment-operator-visitor@7.22.15':
    dependencies:
      '@babel/types': 7.23.6

  '@babel/helper-compilation-targets@7.23.6':
    dependencies:
      '@babel/compat-data': 7.23.5
      '@babel/helper-validator-option': 7.23.5
      browserslist: 4.22.2
      lru-cache: 5.1.1
      semver: 6.3.1

  '@babel/helper-create-class-features-plugin@7.23.6(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-member-expression-to-functions': 7.23.0
      '@babel/helper-optimise-call-expression': 7.22.5
      '@babel/helper-replace-supers': 7.22.20(@babel/core@7.22.9)
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      semver: 6.3.1

  '@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-annotate-as-pure': 7.22.5
      regexpu-core: 5.3.2
      semver: 6.3.1

  '@babel/helper-define-polyfill-provider@0.4.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-plugin-utils': 7.22.5
      debug: 4.3.4
      lodash.debounce: 4.0.8
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  '@babel/helper-environment-visitor@7.22.20': {}

  '@babel/helper-function-name@7.23.0':
    dependencies:
      '@babel/template': 7.22.15
      '@babel/types': 7.23.6

  '@babel/helper-hoist-variables@7.22.5':
    dependencies:
      '@babel/types': 7.23.6

  '@babel/helper-member-expression-to-functions@7.23.0':
    dependencies:
      '@babel/types': 7.23.6

  '@babel/helper-module-imports@7.22.15':
    dependencies:
      '@babel/types': 7.23.6

  '@babel/helper-module-transforms@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-module-imports': 7.22.15
      '@babel/helper-simple-access': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/helper-validator-identifier': 7.22.20

  '@babel/helper-module-transforms@7.23.3(@babel/core@7.23.2)':
    dependencies:
      '@babel/core': 7.23.2
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-module-imports': 7.22.15
      '@babel/helper-simple-access': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/helper-validator-identifier': 7.22.20

  '@babel/helper-module-transforms@7.23.3(@babel/core@7.23.6)':
    dependencies:
      '@babel/core': 7.23.6
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-module-imports': 7.22.15
      '@babel/helper-simple-access': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/helper-validator-identifier': 7.22.20

  '@babel/helper-optimise-call-expression@7.22.5':
    dependencies:
      '@babel/types': 7.23.6

  '@babel/helper-plugin-utils@7.22.5': {}

  '@babel/helper-remap-async-to-generator@7.22.20(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-wrap-function': 7.22.20

  '@babel/helper-replace-supers@7.22.20(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-member-expression-to-functions': 7.23.0
      '@babel/helper-optimise-call-expression': 7.22.5

  '@babel/helper-simple-access@7.22.5':
    dependencies:
      '@babel/types': 7.23.6

  '@babel/helper-skip-transparent-expression-wrappers@7.22.5':
    dependencies:
      '@babel/types': 7.23.6

  '@babel/helper-split-export-declaration@7.22.6':
    dependencies:
      '@babel/types': 7.23.6

  '@babel/helper-string-parser@7.23.4': {}

  '@babel/helper-validator-identifier@7.22.20': {}

  '@babel/helper-validator-option@7.23.5': {}

  '@babel/helper-wrap-function@7.22.20':
    dependencies:
      '@babel/helper-function-name': 7.23.0
      '@babel/template': 7.22.15
      '@babel/types': 7.23.6

  '@babel/helpers@7.23.6':
    dependencies:
      '@babel/template': 7.22.15
      '@babel/traverse': 7.23.6
      '@babel/types': 7.23.6
    transitivePeerDependencies:
      - supports-color

  '@babel/highlight@7.23.4':
    dependencies:
      '@babel/helper-validator-identifier': 7.22.20
      chalk: 2.4.2
      js-tokens: 4.0.0

  '@babel/parser@7.23.6':
    dependencies:
      '@babel/types': 7.23.6

  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-transform-optional-chaining': 7.23.4(@babel/core@7.22.9)

  '@babel/plugin-proposal-async-generator-functions@7.20.7(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-remap-async-to-generator': 7.22.20(@babel/core@7.22.9)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.22.9)

  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9

  '@babel/plugin-proposal-unicode-property-regex@7.18.6(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-import-assertions@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-import-attributes@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-arrow-functions@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-async-generator-functions@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-remap-async-to-generator': 7.22.20(@babel/core@7.22.9)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.22.9)

  '@babel/plugin-transform-async-to-generator@7.22.5(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-module-imports': 7.22.15
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-remap-async-to-generator': 7.22.20(@babel/core@7.22.9)

  '@babel/plugin-transform-block-scoped-functions@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-block-scoping@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-class-properties@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-create-class-features-plugin': 7.23.6(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-class-static-block@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-create-class-features-plugin': 7.23.6(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.22.9)

  '@babel/plugin-transform-classes@7.23.5(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-optimise-call-expression': 7.22.5
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-replace-supers': 7.22.20(@babel/core@7.22.9)
      '@babel/helper-split-export-declaration': 7.22.6
      globals: 11.12.0

  '@babel/plugin-transform-computed-properties@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/template': 7.22.15

  '@babel/plugin-transform-destructuring@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-dotall-regex@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-duplicate-keys@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-dynamic-import@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.22.9)

  '@babel/plugin-transform-exponentiation-operator@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-builder-binary-assignment-operator-visitor': 7.22.15
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-export-namespace-from@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.22.9)

  '@babel/plugin-transform-for-of@7.23.6(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5

  '@babel/plugin-transform-function-name@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-json-strings@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.22.9)

  '@babel/plugin-transform-literals@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-logical-assignment-operators@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.22.9)

  '@babel/plugin-transform-member-expression-literals@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-modules-amd@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-modules-commonjs@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-simple-access': 7.22.5

  '@babel/plugin-transform-modules-systemjs@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-validator-identifier': 7.22.20

  '@babel/plugin-transform-modules-umd@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-module-transforms': 7.23.3(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-named-capturing-groups-regex@7.22.5(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-new-target@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-nullish-coalescing-operator@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.22.9)

  '@babel/plugin-transform-numeric-separator@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.22.9)

  '@babel/plugin-transform-object-rest-spread@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/compat-data': 7.23.5
      '@babel/core': 7.22.9
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.22.9)
      '@babel/plugin-transform-parameters': 7.23.3(@babel/core@7.22.9)

  '@babel/plugin-transform-object-super@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-replace-supers': 7.22.20(@babel/core@7.22.9)

  '@babel/plugin-transform-optional-catch-binding@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.22.9)

  '@babel/plugin-transform-optional-chaining@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.22.9)

  '@babel/plugin-transform-parameters@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-private-methods@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-create-class-features-plugin': 7.23.6(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-private-property-in-object@7.23.4(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-annotate-as-pure': 7.22.5
      '@babel/helper-create-class-features-plugin': 7.23.6(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.22.9)

  '@babel/plugin-transform-property-literals@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-regenerator@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      regenerator-transform: 0.15.2

  '@babel/plugin-transform-reserved-words@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-runtime@7.22.9(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-module-imports': 7.22.15
      '@babel/helper-plugin-utils': 7.22.5
      babel-plugin-polyfill-corejs2: 0.4.7(@babel/core@7.22.9)
      babel-plugin-polyfill-corejs3: 0.8.7(@babel/core@7.22.9)
      babel-plugin-polyfill-regenerator: 0.5.4(@babel/core@7.22.9)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/plugin-transform-shorthand-properties@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-spread@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-skip-transparent-expression-wrappers': 7.22.5

  '@babel/plugin-transform-sticky-regex@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-template-literals@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-typeof-symbol@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-unicode-escapes@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-unicode-property-regex@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-unicode-regex@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/plugin-transform-unicode-sets-regex@7.23.3(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-create-regexp-features-plugin': 7.22.15(@babel/core@7.22.9)
      '@babel/helper-plugin-utils': 7.22.5

  '@babel/preset-env@7.22.9(@babel/core@7.22.9)':
    dependencies:
      '@babel/compat-data': 7.23.5
      '@babel/core': 7.22.9
      '@babel/helper-compilation-targets': 7.23.6
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/helper-validator-option': 7.23.5
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-proposal-private-property-in-object': 7.21.0-placeholder-for-preset-env.2(@babel/core@7.22.9)
      '@babel/plugin-syntax-async-generators': 7.8.4(@babel/core@7.22.9)
      '@babel/plugin-syntax-class-properties': 7.12.13(@babel/core@7.22.9)
      '@babel/plugin-syntax-class-static-block': 7.14.5(@babel/core@7.22.9)
      '@babel/plugin-syntax-dynamic-import': 7.8.3(@babel/core@7.22.9)
      '@babel/plugin-syntax-export-namespace-from': 7.8.3(@babel/core@7.22.9)
      '@babel/plugin-syntax-import-assertions': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-syntax-import-attributes': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-syntax-import-meta': 7.10.4(@babel/core@7.22.9)
      '@babel/plugin-syntax-json-strings': 7.8.3(@babel/core@7.22.9)
      '@babel/plugin-syntax-logical-assignment-operators': 7.10.4(@babel/core@7.22.9)
      '@babel/plugin-syntax-nullish-coalescing-operator': 7.8.3(@babel/core@7.22.9)
      '@babel/plugin-syntax-numeric-separator': 7.10.4(@babel/core@7.22.9)
      '@babel/plugin-syntax-object-rest-spread': 7.8.3(@babel/core@7.22.9)
      '@babel/plugin-syntax-optional-catch-binding': 7.8.3(@babel/core@7.22.9)
      '@babel/plugin-syntax-optional-chaining': 7.8.3(@babel/core@7.22.9)
      '@babel/plugin-syntax-private-property-in-object': 7.14.5(@babel/core@7.22.9)
      '@babel/plugin-syntax-top-level-await': 7.14.5(@babel/core@7.22.9)
      '@babel/plugin-syntax-unicode-sets-regex': 7.18.6(@babel/core@7.22.9)
      '@babel/plugin-transform-arrow-functions': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-async-generator-functions': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-async-to-generator': 7.22.5(@babel/core@7.22.9)
      '@babel/plugin-transform-block-scoped-functions': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-block-scoping': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-class-properties': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-class-static-block': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-classes': 7.23.5(@babel/core@7.22.9)
      '@babel/plugin-transform-computed-properties': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-destructuring': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-dotall-regex': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-duplicate-keys': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-dynamic-import': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-exponentiation-operator': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-export-namespace-from': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-for-of': 7.23.6(@babel/core@7.22.9)
      '@babel/plugin-transform-function-name': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-json-strings': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-literals': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-logical-assignment-operators': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-member-expression-literals': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-modules-amd': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-modules-commonjs': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-modules-systemjs': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-modules-umd': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-named-capturing-groups-regex': 7.22.5(@babel/core@7.22.9)
      '@babel/plugin-transform-new-target': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-nullish-coalescing-operator': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-numeric-separator': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-object-rest-spread': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-object-super': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-optional-catch-binding': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-optional-chaining': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-parameters': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-private-methods': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-private-property-in-object': 7.23.4(@babel/core@7.22.9)
      '@babel/plugin-transform-property-literals': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-regenerator': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-reserved-words': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-shorthand-properties': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-spread': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-sticky-regex': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-template-literals': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-typeof-symbol': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-unicode-escapes': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-unicode-property-regex': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-unicode-regex': 7.23.3(@babel/core@7.22.9)
      '@babel/plugin-transform-unicode-sets-regex': 7.23.3(@babel/core@7.22.9)
      '@babel/preset-modules': 0.1.6(@babel/core@7.22.9)
      '@babel/types': 7.23.6
      babel-plugin-polyfill-corejs2: 0.4.7(@babel/core@7.22.9)
      babel-plugin-polyfill-corejs3: 0.8.7(@babel/core@7.22.9)
      babel-plugin-polyfill-regenerator: 0.5.4(@babel/core@7.22.9)
      core-js-compat: 3.34.0
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  '@babel/preset-modules@0.1.6(@babel/core@7.22.9)':
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-plugin-utils': 7.22.5
      '@babel/plugin-proposal-unicode-property-regex': 7.18.6(@babel/core@7.22.9)
      '@babel/plugin-transform-dotall-regex': 7.23.3(@babel/core@7.22.9)
      '@babel/types': 7.23.6
      esutils: 2.0.3

  '@babel/regjsgen@0.8.0': {}

  '@babel/runtime@7.22.6':
    dependencies:
      regenerator-runtime: 0.13.11

  '@babel/template@7.22.15':
    dependencies:
      '@babel/code-frame': 7.23.5
      '@babel/parser': 7.23.6
      '@babel/types': 7.23.6

  '@babel/template@7.22.5':
    dependencies:
      '@babel/code-frame': 7.23.5
      '@babel/parser': 7.23.6
      '@babel/types': 7.23.6

  '@babel/traverse@7.23.6':
    dependencies:
      '@babel/code-frame': 7.23.5
      '@babel/generator': 7.23.6
      '@babel/helper-environment-visitor': 7.22.20
      '@babel/helper-function-name': 7.23.0
      '@babel/helper-hoist-variables': 7.22.5
      '@babel/helper-split-export-declaration': 7.22.6
      '@babel/parser': 7.23.6
      '@babel/types': 7.23.6
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  '@babel/types@7.23.6':
    dependencies:
      '@babel/helper-string-parser': 7.23.4
      '@babel/helper-validator-identifier': 7.22.20
      to-fast-properties: 2.0.0

  '@colors/colors@1.5.0': {}

  '@discoveryjs/json-ext@0.5.7': {}

  '@esbuild/android-arm64@0.18.17':
    optional: true

  '@esbuild/android-arm@0.18.17':
    optional: true

  '@esbuild/android-x64@0.18.17':
    optional: true

  '@esbuild/darwin-arm64@0.18.17':
    optional: true

  '@esbuild/darwin-x64@0.18.17':
    optional: true

  '@esbuild/freebsd-arm64@0.18.17':
    optional: true

  '@esbuild/freebsd-x64@0.18.17':
    optional: true

  '@esbuild/linux-arm64@0.18.17':
    optional: true

  '@esbuild/linux-arm@0.18.17':
    optional: true

  '@esbuild/linux-ia32@0.18.17':
    optional: true

  '@esbuild/linux-loong64@0.18.17':
    optional: true

  '@esbuild/linux-mips64el@0.18.17':
    optional: true

  '@esbuild/linux-ppc64@0.18.17':
    optional: true

  '@esbuild/linux-riscv64@0.18.17':
    optional: true

  '@esbuild/linux-s390x@0.18.17':
    optional: true

  '@esbuild/linux-x64@0.18.17':
    optional: true

  '@esbuild/netbsd-x64@0.18.17':
    optional: true

  '@esbuild/openbsd-x64@0.18.17':
    optional: true

  '@esbuild/sunos-x64@0.18.17':
    optional: true

  '@esbuild/win32-arm64@0.18.17':
    optional: true

  '@esbuild/win32-ia32@0.18.17':
    optional: true

  '@esbuild/win32-x64@0.18.17':
    optional: true

  '@gar/promisify@1.1.3': {}

  '@isaacs/cliui@8.0.2':
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  '@istanbuljs/load-nyc-config@1.1.0':
    dependencies:
      camelcase: 5.3.1
      find-up: 4.1.0
      get-package-type: 0.1.0
      js-yaml: 3.14.1
      resolve-from: 5.0.0

  '@istanbuljs/schema@0.1.3': {}

  '@jridgewell/gen-mapping@0.3.3':
    dependencies:
      '@jridgewell/set-array': 1.1.2
      '@jridgewell/sourcemap-codec': 1.4.15
      '@jridgewell/trace-mapping': 0.3.20

  '@jridgewell/resolve-uri@3.1.1': {}

  '@jridgewell/set-array@1.1.2': {}

  '@jridgewell/source-map@0.3.5':
    dependencies:
      '@jridgewell/gen-mapping': 0.3.3
      '@jridgewell/trace-mapping': 0.3.20

  '@jridgewell/sourcemap-codec@1.4.15': {}

  '@jridgewell/trace-mapping@0.3.20':
    dependencies:
      '@jridgewell/resolve-uri': 3.1.1
      '@jridgewell/sourcemap-codec': 1.4.15

  '@leichtgewicht/ip-codec@2.0.4': {}

  '@material/animation@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      tslib: 2.6.2

  '@material/auto-init@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/banner@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/button': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/base@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      tslib: 2.6.2

  '@material/button@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/focus-ring': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/touch-target': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/card@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/checkbox@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/focus-ring': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/touch-target': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/chips@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/checkbox': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/focus-ring': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/touch-target': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      safevalues: 0.3.4
      tslib: 2.6.2

  '@material/circular-progress@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/progress-indicator': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/data-table@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/checkbox': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/icon-button': 15.0.0-canary.bc9ae6c9c.0
      '@material/linear-progress': 15.0.0-canary.bc9ae6c9c.0
      '@material/list': 15.0.0-canary.bc9ae6c9c.0
      '@material/menu': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/select': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/touch-target': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/density@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      tslib: 2.6.2

  '@material/dialog@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/button': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/icon-button': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/touch-target': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/dom@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/drawer@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/list': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/elevation@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/fab@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/focus-ring': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/touch-target': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/feature-targeting@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      tslib: 2.6.2

  '@material/floating-label@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/focus-ring@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0

  '@material/form-field@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/icon-button@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/focus-ring': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/touch-target': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/image-list@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/layout-grid@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      tslib: 2.6.2

  '@material/line-ripple@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/linear-progress@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/progress-indicator': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/list@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/menu-surface@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/menu@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/list': 15.0.0-canary.bc9ae6c9c.0
      '@material/menu-surface': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/notched-outline@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/floating-label': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/progress-indicator@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      tslib: 2.6.2

  '@material/radio@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/focus-ring': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/touch-target': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/ripple@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/rtl@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/segmented-button@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/touch-target': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/select@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/floating-label': 15.0.0-canary.bc9ae6c9c.0
      '@material/line-ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/list': 15.0.0-canary.bc9ae6c9c.0
      '@material/menu': 15.0.0-canary.bc9ae6c9c.0
      '@material/menu-surface': 15.0.0-canary.bc9ae6c9c.0
      '@material/notched-outline': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/shape@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/slider@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/snackbar@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/button': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/icon-button': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/switch@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/focus-ring': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      safevalues: 0.3.4
      tslib: 2.6.2

  '@material/tab-bar@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/tab': 15.0.0-canary.bc9ae6c9c.0
      '@material/tab-indicator': 15.0.0-canary.bc9ae6c9c.0
      '@material/tab-scroller': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/tab-indicator@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/tab-scroller@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/tab': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/tab@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/focus-ring': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/tab-indicator': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/textfield@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/density': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/floating-label': 15.0.0-canary.bc9ae6c9c.0
      '@material/line-ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/notched-outline': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/theme@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/tokens@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0

  '@material/tooltip@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/button': 15.0.0-canary.bc9ae6c9c.0
      '@material/dom': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/tokens': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      safevalues: 0.3.4
      tslib: 2.6.2

  '@material/top-app-bar@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/animation': 15.0.0-canary.bc9ae6c9c.0
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/elevation': 15.0.0-canary.bc9ae6c9c.0
      '@material/ripple': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/shape': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      '@material/typography': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/touch-target@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/base': 15.0.0-canary.bc9ae6c9c.0
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/rtl': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@material/typography@15.0.0-canary.bc9ae6c9c.0':
    dependencies:
      '@material/feature-targeting': 15.0.0-canary.bc9ae6c9c.0
      '@material/theme': 15.0.0-canary.bc9ae6c9c.0
      tslib: 2.6.2

  '@mattlewis92/dom-autoscroller@2.4.2': {}

  '@ngtools/webpack@16.2.14(@angular/compiler-cli@16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6))(typescript@5.1.6)(webpack@5.88.2(esbuild@0.18.17))':
    dependencies:
      '@angular/compiler-cli': 16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6)
      typescript: 5.1.6
      webpack: 5.88.2(esbuild@0.18.17)

  ? '@nguniversal/builders@16.2.0(@angular-devkit/build-angular@16.2.14(@angular/compiler-cli@16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6))(@angular/platform-server@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))))(@types/node@16.18.97)(karma@6.4.2)(typescript@5.1.6))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@types/express@4.17.21)(chokidar@3.5.3)(typescript@5.1.6)'
  : dependencies:
      '@angular-devkit/architect': 0.1602.11(chokidar@3.5.3)
      '@angular-devkit/build-angular': 16.2.14(@angular/compiler-cli@16.2.12(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(typescript@5.1.6))(@angular/platform-server@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/compiler@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))))(@types/node@16.18.97)(karma@6.4.2)(typescript@5.1.6)
      '@angular-devkit/core': 16.2.11(chokidar@3.5.3)
      '@nguniversal/common': 16.2.0(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      browser-sync: 2.29.3
      express: 4.18.2
      guess-parser: 0.4.22(typescript@5.1.6)
      http-proxy-middleware: 2.0.6(@types/express@4.17.21)
      ora: 5.4.1
      piscina: 4.0.0
      rxjs: 7.8.1
      tree-kill: 1.2.2
    transitivePeerDependencies:
      - '@angular/common'
      - '@angular/core'
      - '@types/express'
      - bufferutil
      - canvas
      - chokidar
      - debug
      - supports-color
      - typescript
      - utf-8-validate

  '@nguniversal/common@16.2.0(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))':
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      critters: 0.0.20
      jsdom: 22.1.0
      tslib: 2.6.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  '@nguniversal/express-engine@16.2.0(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(express@4.18.2)':
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@nguniversal/common': 16.2.0(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      express: 4.18.2
      tslib: 2.6.2
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  '@nodelib/fs.scandir@2.1.5':
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      run-parallel: 1.2.0

  '@nodelib/fs.stat@2.0.5': {}

  '@nodelib/fs.walk@1.2.8':
    dependencies:
      '@nodelib/fs.scandir': 2.1.5
      fastq: 1.16.0

  '@npmcli/fs@2.1.2':
    dependencies:
      '@gar/promisify': 1.1.3
      semver: 7.5.4

  '@npmcli/fs@3.1.0':
    dependencies:
      semver: 7.5.4

  '@npmcli/git@4.1.0':
    dependencies:
      '@npmcli/promise-spawn': 6.0.2
      lru-cache: 7.18.3
      npm-pick-manifest: 8.0.1
      proc-log: 3.0.0
      promise-inflight: 1.0.1
      promise-retry: 2.0.1
      semver: 7.5.4
      which: 3.0.1
    transitivePeerDependencies:
      - bluebird

  '@npmcli/installed-package-contents@2.0.2':
    dependencies:
      npm-bundled: 3.0.0
      npm-normalize-package-bin: 3.0.1

  '@npmcli/move-file@2.0.1':
    dependencies:
      mkdirp: 1.0.4
      rimraf: 3.0.2

  '@npmcli/node-gyp@3.0.0': {}

  '@npmcli/promise-spawn@6.0.2':
    dependencies:
      which: 3.0.1

  '@npmcli/run-script@6.0.2':
    dependencies:
      '@npmcli/node-gyp': 3.0.0
      '@npmcli/promise-spawn': 6.0.2
      node-gyp: 9.4.1
      read-package-json-fast: 3.0.2
      which: 3.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color

  '@pkgjs/parseargs@0.11.0':
    optional: true

  '@scarf/scarf@1.4.0': {}

  '@schematics/angular@16.2.14(chokidar@3.5.3)':
    dependencies:
      '@angular-devkit/core': 16.2.14(chokidar@3.5.3)
      '@angular-devkit/schematics': 16.2.14(chokidar@3.5.3)
      jsonc-parser: 3.2.0
    transitivePeerDependencies:
      - chokidar

  '@sigstore/bundle@1.1.0':
    dependencies:
      '@sigstore/protobuf-specs': 0.2.1

  '@sigstore/protobuf-specs@0.2.1': {}

  '@sigstore/sign@1.0.0':
    dependencies:
      '@sigstore/bundle': 1.1.0
      '@sigstore/protobuf-specs': 0.2.1
      make-fetch-happen: 11.1.1
    transitivePeerDependencies:
      - supports-color

  '@sigstore/tuf@1.0.3':
    dependencies:
      '@sigstore/protobuf-specs': 0.2.1
      tuf-js: 1.1.7
    transitivePeerDependencies:
      - supports-color

  '@socket.io/component-emitter@3.1.0': {}

  '@tootallnate/once@1.1.2': {}

  '@tootallnate/once@2.0.0': {}

  '@tufjs/canonical-json@1.0.0': {}

  '@tufjs/models@1.0.4':
    dependencies:
      '@tufjs/canonical-json': 1.0.0
      minimatch: 9.0.3

  '@types/body-parser@1.19.5':
    dependencies:
      '@types/connect': 3.4.38
      '@types/node': 16.18.97

  '@types/bonjour@3.5.13':
    dependencies:
      '@types/node': 16.18.97

  '@types/connect-history-api-fallback@1.5.4':
    dependencies:
      '@types/express-serve-static-core': 4.17.41
      '@types/node': 16.18.97

  '@types/connect@3.4.38':
    dependencies:
      '@types/node': 16.18.97

  '@types/cookie@0.4.1': {}

  '@types/cors@2.8.17':
    dependencies:
      '@types/node': 16.18.97

  '@types/eslint-scope@3.7.7':
    dependencies:
      '@types/eslint': 8.56.0
      '@types/estree': 1.0.5

  '@types/eslint@8.56.0':
    dependencies:
      '@types/estree': 1.0.5
      '@types/json-schema': 7.0.15

  '@types/estree@1.0.5': {}

  '@types/express-serve-static-core@4.17.41':
    dependencies:
      '@types/node': 16.18.97
      '@types/qs': 6.9.11
      '@types/range-parser': 1.2.7
      '@types/send': 0.17.4

  '@types/express@4.17.21':
    dependencies:
      '@types/body-parser': 1.19.5
      '@types/express-serve-static-core': 4.17.41
      '@types/qs': 6.9.11
      '@types/serve-static': 1.15.5

  '@types/http-errors@2.0.4': {}

  '@types/http-proxy@1.17.14':
    dependencies:
      '@types/node': 16.18.97

  '@types/jasmine@4.3.6': {}

  '@types/json-schema@7.0.15': {}

  '@types/mime@1.3.5': {}

  '@types/mime@3.0.4': {}

  '@types/node-forge@1.3.10':
    dependencies:
      '@types/node': 16.18.97

  '@types/node@16.18.97': {}

  '@types/qs@6.9.11': {}

  '@types/range-parser@1.2.7': {}

  '@types/retry@0.12.0': {}

  '@types/send@0.17.4':
    dependencies:
      '@types/mime': 1.3.5
      '@types/node': 16.18.97

  '@types/serve-index@1.9.4':
    dependencies:
      '@types/express': 4.17.21

  '@types/serve-static@1.15.5':
    dependencies:
      '@types/http-errors': 2.0.4
      '@types/mime': 3.0.4
      '@types/node': 16.18.97

  '@types/sockjs@0.3.36':
    dependencies:
      '@types/node': 16.18.97

  '@types/ws@8.5.10':
    dependencies:
      '@types/node': 16.18.97

  '@vitejs/plugin-basic-ssl@1.0.1(vite@4.5.3(@types/node@16.18.97)(less@4.1.3)(sass@1.64.1)(terser@5.19.2))':
    dependencies:
      vite: 4.5.3(@types/node@16.18.97)(less@4.1.3)(sass@1.64.1)(terser@5.19.2)

  '@webassemblyjs/ast@1.11.6':
    dependencies:
      '@webassemblyjs/helper-numbers': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6

  '@webassemblyjs/floating-point-hex-parser@1.11.6': {}

  '@webassemblyjs/helper-api-error@1.11.6': {}

  '@webassemblyjs/helper-buffer@1.11.6': {}

  '@webassemblyjs/helper-numbers@1.11.6':
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': 1.11.6
      '@webassemblyjs/helper-api-error': 1.11.6
      '@xtuc/long': 4.2.2

  '@webassemblyjs/helper-wasm-bytecode@1.11.6': {}

  '@webassemblyjs/helper-wasm-section@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/helper-buffer': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/wasm-gen': 1.11.6

  '@webassemblyjs/ieee754@1.11.6':
    dependencies:
      '@xtuc/ieee754': 1.2.0

  '@webassemblyjs/leb128@1.11.6':
    dependencies:
      '@xtuc/long': 4.2.2

  '@webassemblyjs/utf8@1.11.6': {}

  '@webassemblyjs/wasm-edit@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/helper-buffer': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/helper-wasm-section': 1.11.6
      '@webassemblyjs/wasm-gen': 1.11.6
      '@webassemblyjs/wasm-opt': 1.11.6
      '@webassemblyjs/wasm-parser': 1.11.6
      '@webassemblyjs/wast-printer': 1.11.6

  '@webassemblyjs/wasm-gen@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/ieee754': 1.11.6
      '@webassemblyjs/leb128': 1.11.6
      '@webassemblyjs/utf8': 1.11.6

  '@webassemblyjs/wasm-opt@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/helper-buffer': 1.11.6
      '@webassemblyjs/wasm-gen': 1.11.6
      '@webassemblyjs/wasm-parser': 1.11.6

  '@webassemblyjs/wasm-parser@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/helper-api-error': 1.11.6
      '@webassemblyjs/helper-wasm-bytecode': 1.11.6
      '@webassemblyjs/ieee754': 1.11.6
      '@webassemblyjs/leb128': 1.11.6
      '@webassemblyjs/utf8': 1.11.6

  '@webassemblyjs/wast-printer@1.11.6':
    dependencies:
      '@webassemblyjs/ast': 1.11.6
      '@xtuc/long': 4.2.2

  '@wessberg/ts-evaluator@0.0.27(typescript@5.1.6)':
    dependencies:
      chalk: 4.1.2
      jsdom: 16.7.0
      object-path: 0.11.8
      tslib: 2.6.2
      typescript: 5.1.6
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  '@xtuc/ieee754@1.2.0': {}

  '@xtuc/long@4.2.2': {}

  '@yarnpkg/lockfile@1.1.0': {}

  abab@2.0.6: {}

  abbrev@1.1.1: {}

  accepts@1.3.8:
    dependencies:
      mime-types: 2.1.35
      negotiator: 0.6.3

  acorn-globals@6.0.0:
    dependencies:
      acorn: 7.4.1
      acorn-walk: 7.2.0

  acorn-import-assertions@1.9.0(acorn@8.11.2):
    dependencies:
      acorn: 8.11.2

  acorn-walk@7.2.0: {}

  acorn@7.4.1: {}

  acorn@8.11.2: {}

  adjust-sourcemap-loader@4.0.0:
    dependencies:
      loader-utils: 2.0.4
      regex-parser: 2.2.11

  agent-base@6.0.2:
    dependencies:
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  agentkeepalive@4.5.0:
    dependencies:
      humanize-ms: 1.2.1

  aggregate-error@3.1.0:
    dependencies:
      clean-stack: 2.2.0
      indent-string: 4.0.0

  ajv-formats@2.1.1(ajv@8.12.0):
    optionalDependencies:
      ajv: 8.12.0

  ajv-keywords@3.5.2(ajv@6.12.6):
    dependencies:
      ajv: 6.12.6

  ajv-keywords@5.1.0(ajv@8.12.0):
    dependencies:
      ajv: 8.12.0
      fast-deep-equal: 3.1.3

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.12.0:
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1

  angular-calendar@0.31.1(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)):
    dependencies:
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@scarf/scarf': 1.4.0
      angular-draggable-droppable: 8.0.0(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      angular-resizable-element: 7.0.2(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      calendar-utils: 0.10.4
      positioning: 2.0.1
      tslib: 2.6.2

  angular-draggable-droppable@8.0.0(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)):
    dependencies:
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@mattlewis92/dom-autoscroller': 2.4.2
      tslib: 2.6.2

  angular-resizable-element@7.0.2(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)):
    dependencies:
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      tslib: 2.6.2

  ansi-colors@4.1.3: {}

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-html-community@0.0.8: {}

  ansi-regex@5.0.1: {}

  ansi-regex@6.0.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@6.2.1: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  aproba@2.0.0: {}

  are-we-there-yet@3.0.1:
    dependencies:
      delegates: 1.0.0
      readable-stream: 3.6.2

  argparse@1.0.10:
    dependencies:
      sprintf-js: 1.0.3

  argparse@2.0.1: {}

  array-flatten@1.1.1: {}

  array-flatten@2.1.2: {}

  async-each-series@0.1.1: {}

  async@2.6.4:
    dependencies:
      lodash: 4.17.21

  asynckit@0.4.0: {}

  autoprefixer@10.4.14(postcss@8.4.31):
    dependencies:
      browserslist: 4.22.2
      caniuse-lite: 1.0.30001571
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.0.0
      postcss: 8.4.31
      postcss-value-parser: 4.2.0

  axios@0.21.4(debug@4.3.2):
    dependencies:
      follow-redirects: 1.15.3(debug@4.3.2)
    transitivePeerDependencies:
      - debug

  babel-loader@9.1.3(@babel/core@7.22.9)(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      '@babel/core': 7.22.9
      find-cache-dir: 4.0.0
      schema-utils: 4.2.0
      webpack: 5.88.2(esbuild@0.18.17)

  babel-plugin-istanbul@6.1.1:
    dependencies:
      '@babel/helper-plugin-utils': 7.22.5
      '@istanbuljs/load-nyc-config': 1.1.0
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-instrument: 5.2.1
      test-exclude: 6.0.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs2@0.4.7(@babel/core@7.22.9):
    dependencies:
      '@babel/compat-data': 7.23.5
      '@babel/core': 7.22.9
      '@babel/helper-define-polyfill-provider': 0.4.4(@babel/core@7.22.9)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.8.7(@babel/core@7.22.9):
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-define-polyfill-provider': 0.4.4(@babel/core@7.22.9)
      core-js-compat: 3.34.0
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.5.4(@babel/core@7.22.9):
    dependencies:
      '@babel/core': 7.22.9
      '@babel/helper-define-polyfill-provider': 0.4.4(@babel/core@7.22.9)
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  base64-js@1.5.1: {}

  base64id@2.0.0: {}

  batch@0.6.1: {}

  big.js@5.2.2: {}

  binary-extensions@2.2.0: {}

  bl@4.1.0:
    dependencies:
      buffer: 5.7.1
      inherits: 2.0.4
      readable-stream: 3.6.2

  body-parser@1.20.1:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.11.0
      raw-body: 2.5.1
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  body-parser@1.20.2:
    dependencies:
      bytes: 3.1.2
      content-type: 1.0.5
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      on-finished: 2.4.1
      qs: 6.11.0
      raw-body: 2.5.2
      type-is: 1.6.18
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  bonjour-service@1.1.1:
    dependencies:
      array-flatten: 2.1.2
      dns-equal: 1.0.0
      fast-deep-equal: 3.1.3
      multicast-dns: 7.2.5

  boolbase@1.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.2:
    dependencies:
      fill-range: 7.0.1

  browser-process-hrtime@1.0.0: {}

  browser-sync-client@2.29.3:
    dependencies:
      etag: 1.8.1
      fresh: 0.5.2
      mitt: 1.2.0

  browser-sync-ui@2.29.3:
    dependencies:
      async-each-series: 0.1.1
      chalk: 4.1.2
      connect-history-api-fallback: 1.6.0
      immutable: 3.8.2
      server-destroy: 1.0.1
      socket.io-client: 4.7.5
      stream-throttle: 0.1.3
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  browser-sync@2.29.3:
    dependencies:
      browser-sync-client: 2.29.3
      browser-sync-ui: 2.29.3
      bs-recipes: 1.3.4
      chalk: 4.1.2
      chokidar: 3.5.3
      connect: 3.6.6
      connect-history-api-fallback: 1.6.0
      dev-ip: 1.0.1
      easy-extender: 2.3.4
      eazy-logger: 4.0.1
      etag: 1.8.1
      fresh: 0.5.2
      fs-extra: 3.0.1
      http-proxy: 1.18.1
      immutable: 3.8.2
      localtunnel: 2.0.2
      micromatch: 4.0.5
      opn: 5.3.0
      portscanner: 2.2.0
      raw-body: 2.5.2
      resp-modifier: 6.0.2
      rx: 4.1.0
      send: 0.16.2
      serve-index: 1.9.1
      serve-static: 1.13.2
      server-destroy: 1.0.1
      socket.io: 4.7.2
      ua-parser-js: 1.0.37
      yargs: 17.7.2
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  browserslist@4.22.2:
    dependencies:
      caniuse-lite: 1.0.30001571
      electron-to-chromium: 1.4.616
      node-releases: 2.0.14
      update-browserslist-db: 1.0.13(browserslist@4.22.2)

  bs-recipes@1.3.4: {}

  buffer-from@1.1.2: {}

  buffer@5.7.1:
    dependencies:
      base64-js: 1.5.1
      ieee754: 1.2.1

  builtins@5.0.1:
    dependencies:
      semver: 7.5.4

  bytes@3.0.0: {}

  bytes@3.1.2: {}

  cacache@16.1.3:
    dependencies:
      '@npmcli/fs': 2.1.2
      '@npmcli/move-file': 2.0.1
      chownr: 2.0.0
      fs-minipass: 2.1.0
      glob: 8.1.0
      infer-owner: 1.0.4
      lru-cache: 7.18.3
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      mkdirp: 1.0.4
      p-map: 4.0.0
      promise-inflight: 1.0.1
      rimraf: 3.0.2
      ssri: 9.0.1
      tar: 6.2.0
      unique-filename: 2.0.1
    transitivePeerDependencies:
      - bluebird

  cacache@17.1.4:
    dependencies:
      '@npmcli/fs': 3.1.0
      fs-minipass: 3.0.3
      glob: 10.3.10
      lru-cache: 7.18.3
      minipass: 7.0.4
      minipass-collect: 1.0.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      p-map: 4.0.0
      ssri: 10.0.5
      tar: 6.2.0
      unique-filename: 3.0.0

  calendar-utils@0.10.4: {}

  call-bind@1.0.5:
    dependencies:
      function-bind: 1.1.2
      get-intrinsic: 1.2.2
      set-function-length: 1.1.1

  callsites@3.1.0: {}

  camelcase@5.3.1: {}

  caniuse-lite@1.0.30001571: {}

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chardet@0.7.0: {}

  chart.js@3.9.1: {}

  chokidar@3.5.3:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chownr@2.0.0: {}

  chrome-trace-event@1.0.3: {}

  clean-stack@2.2.0: {}

  cli-cursor@3.1.0:
    dependencies:
      restore-cursor: 3.1.0

  cli-spinners@2.9.2: {}

  cli-width@3.0.0: {}

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clone-deep@4.0.1:
    dependencies:
      is-plain-object: 2.0.4
      kind-of: 6.0.3
      shallow-clone: 3.0.1

  clone@1.0.4: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  color-support@1.1.3: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@2.20.3: {}

  common-path-prefix@3.0.0: {}

  compressible@2.0.18:
    dependencies:
      mime-db: 1.52.0

  compression@1.7.4:
    dependencies:
      accepts: 1.3.8
      bytes: 3.0.0
      compressible: 2.0.18
      debug: 2.6.9
      on-headers: 1.0.2
      safe-buffer: 5.1.2
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  concat-map@0.0.1: {}

  connect-history-api-fallback@1.6.0: {}

  connect-history-api-fallback@2.0.0: {}

  connect@3.6.6:
    dependencies:
      debug: 2.6.9
      finalhandler: 1.1.0
      parseurl: 1.3.3
      utils-merge: 1.0.1
    transitivePeerDependencies:
      - supports-color

  connect@3.7.0:
    dependencies:
      debug: 2.6.9
      finalhandler: 1.1.2
      parseurl: 1.3.3
      utils-merge: 1.0.1
    transitivePeerDependencies:
      - supports-color

  console-control-strings@1.1.0: {}

  content-disposition@0.5.4:
    dependencies:
      safe-buffer: 5.2.1

  content-type@1.0.5: {}

  convert-source-map@1.9.0: {}

  convert-source-map@2.0.0: {}

  cookie-signature@1.0.6: {}

  cookie@0.4.2: {}

  cookie@0.5.0: {}

  copy-anything@2.0.6:
    dependencies:
      is-what: 3.14.1

  copy-webpack-plugin@11.0.0(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      fast-glob: 3.3.1
      glob-parent: 6.0.2
      globby: 13.2.2
      normalize-path: 3.0.0
      schema-utils: 4.2.0
      serialize-javascript: 6.0.1
      webpack: 5.88.2(esbuild@0.18.17)

  core-js-compat@3.34.0:
    dependencies:
      browserslist: 4.22.2

  core-util-is@1.0.3: {}

  cors@2.8.5:
    dependencies:
      object-assign: 4.1.1
      vary: 1.1.2

  cosmiconfig@8.3.6(typescript@5.1.6):
    dependencies:
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      parse-json: 5.2.0
      path-type: 4.0.0
    optionalDependencies:
      typescript: 5.1.6

  critters@0.0.20:
    dependencies:
      chalk: 4.1.2
      css-select: 5.1.0
      dom-serializer: 2.0.0
      domhandler: 5.0.3
      htmlparser2: 8.0.2
      postcss: 8.4.31
      pretty-bytes: 5.6.0

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css-loader@6.8.1(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.31)
      postcss: 8.4.31
      postcss-modules-extract-imports: 3.0.0(postcss@8.4.31)
      postcss-modules-local-by-default: 4.0.3(postcss@8.4.31)
      postcss-modules-scope: 3.1.0(postcss@8.4.31)
      postcss-modules-values: 4.0.0(postcss@8.4.31)
      postcss-value-parser: 4.2.0
      semver: 7.5.4
      webpack: 5.88.2(esbuild@0.18.17)

  css-select@5.1.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.1.0
      nth-check: 2.1.1

  css-what@6.1.0: {}

  cssesc@3.0.0: {}

  cssom@0.3.8: {}

  cssom@0.4.4: {}

  cssstyle@2.3.0:
    dependencies:
      cssom: 0.3.8

  cssstyle@3.0.0:
    dependencies:
      rrweb-cssom: 0.6.0

  custom-event@1.0.1: {}

  data-urls@2.0.0:
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 2.3.0
      whatwg-url: 8.7.0

  data-urls@4.0.0:
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 3.0.0
      whatwg-url: 12.0.1

  date-fns@3.6.0: {}

  date-format@4.0.14: {}

  debug@2.6.9:
    dependencies:
      ms: 2.0.0

  debug@4.3.2:
    dependencies:
      ms: 2.1.2

  debug@4.3.4:
    dependencies:
      ms: 2.1.2

  decimal.js@10.4.3: {}

  default-gateway@6.0.3:
    dependencies:
      execa: 5.1.1

  defaults@1.0.4:
    dependencies:
      clone: 1.0.4

  define-data-property@1.1.1:
    dependencies:
      get-intrinsic: 1.2.2
      gopd: 1.0.1
      has-property-descriptors: 1.0.1

  define-lazy-prop@2.0.0: {}

  delayed-stream@1.0.0: {}

  delegates@1.0.0: {}

  depd@1.1.2: {}

  depd@2.0.0: {}

  destroy@1.0.4: {}

  destroy@1.2.0: {}

  detect-node@2.1.0: {}

  dev-ip@1.0.1: {}

  di@0.0.1: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dns-equal@1.0.0: {}

  dns-packet@5.6.1:
    dependencies:
      '@leichtgewicht/ip-codec': 2.0.4

  dom-serialize@2.2.1:
    dependencies:
      custom-event: 1.0.1
      ent: 2.2.0
      extend: 3.0.2
      void-elements: 2.0.1

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  domelementtype@2.3.0: {}

  domexception@2.0.1:
    dependencies:
      webidl-conversions: 5.0.0

  domexception@4.0.0:
    dependencies:
      webidl-conversions: 7.0.0

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@3.1.0:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  eastasianwidth@0.2.0: {}

  easy-extender@2.3.4:
    dependencies:
      lodash: 4.17.21

  eazy-logger@4.0.1:
    dependencies:
      chalk: 4.1.2

  ee-first@1.1.1: {}

  electron-to-chromium@1.4.616: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  emojis-list@3.0.0: {}

  encodeurl@1.0.2: {}

  encoding@0.1.13:
    dependencies:
      iconv-lite: 0.6.3
    optional: true

  engine.io-client@6.5.3:
    dependencies:
      '@socket.io/component-emitter': 3.1.0
      debug: 4.3.4
      engine.io-parser: 5.2.1
      ws: 8.11.0
      xmlhttprequest-ssl: 2.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  engine.io-parser@5.2.1: {}

  engine.io@6.5.4:
    dependencies:
      '@types/cookie': 0.4.1
      '@types/cors': 2.8.17
      '@types/node': 16.18.97
      accepts: 1.3.8
      base64id: 2.0.0
      cookie: 0.4.2
      cors: 2.8.5
      debug: 4.3.4
      engine.io-parser: 5.2.1
      ws: 8.11.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  enhanced-resolve@5.15.0:
    dependencies:
      graceful-fs: 4.2.11
      tapable: 2.2.1

  ent@2.2.0: {}

  entities@4.5.0: {}

  env-paths@2.2.1: {}

  err-code@2.0.3: {}

  errno@0.1.8:
    dependencies:
      prr: 1.0.1
    optional: true

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  es-module-lexer@1.4.1: {}

  esbuild-wasm@0.18.17: {}

  esbuild@0.18.17:
    optionalDependencies:
      '@esbuild/android-arm': 0.18.17
      '@esbuild/android-arm64': 0.18.17
      '@esbuild/android-x64': 0.18.17
      '@esbuild/darwin-arm64': 0.18.17
      '@esbuild/darwin-x64': 0.18.17
      '@esbuild/freebsd-arm64': 0.18.17
      '@esbuild/freebsd-x64': 0.18.17
      '@esbuild/linux-arm': 0.18.17
      '@esbuild/linux-arm64': 0.18.17
      '@esbuild/linux-ia32': 0.18.17
      '@esbuild/linux-loong64': 0.18.17
      '@esbuild/linux-mips64el': 0.18.17
      '@esbuild/linux-ppc64': 0.18.17
      '@esbuild/linux-riscv64': 0.18.17
      '@esbuild/linux-s390x': 0.18.17
      '@esbuild/linux-x64': 0.18.17
      '@esbuild/netbsd-x64': 0.18.17
      '@esbuild/openbsd-x64': 0.18.17
      '@esbuild/sunos-x64': 0.18.17
      '@esbuild/win32-arm64': 0.18.17
      '@esbuild/win32-ia32': 0.18.17
      '@esbuild/win32-x64': 0.18.17

  escalade@3.1.1: {}

  escape-html@1.0.3: {}

  escape-string-regexp@1.0.5: {}

  escodegen@2.1.0:
    dependencies:
      esprima: 4.0.1
      estraverse: 5.3.0
      esutils: 2.0.3
    optionalDependencies:
      source-map: 0.6.1

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  esprima@4.0.1: {}

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  esutils@2.0.3: {}

  etag@1.8.1: {}

  eventemitter-asyncresource@1.0.0: {}

  eventemitter3@4.0.7: {}

  events@3.3.0: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  exponential-backoff@3.1.1: {}

  express@4.18.2:
    dependencies:
      accepts: 1.3.8
      array-flatten: 1.1.1
      body-parser: 1.20.1
      content-disposition: 0.5.4
      content-type: 1.0.5
      cookie: 0.5.0
      cookie-signature: 1.0.6
      debug: 2.6.9
      depd: 2.0.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      finalhandler: 1.2.0
      fresh: 0.5.2
      http-errors: 2.0.0
      merge-descriptors: 1.0.1
      methods: 1.1.2
      on-finished: 2.4.1
      parseurl: 1.3.3
      path-to-regexp: 0.1.7
      proxy-addr: 2.0.7
      qs: 6.11.0
      range-parser: 1.2.1
      safe-buffer: 5.2.1
      send: 0.18.0
      serve-static: 1.15.0
      setprototypeof: 1.2.0
      statuses: 2.0.1
      type-is: 1.6.18
      utils-merge: 1.0.1
      vary: 1.1.2
    transitivePeerDependencies:
      - supports-color

  extend@3.0.2: {}

  external-editor@3.1.0:
    dependencies:
      chardet: 0.7.0
      iconv-lite: 0.4.24
      tmp: 0.0.33

  fast-deep-equal@3.1.3: {}

  fast-glob@3.3.1:
    dependencies:
      '@nodelib/fs.stat': 2.0.5
      '@nodelib/fs.walk': 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5

  fast-json-stable-stringify@2.1.0: {}

  fastq@1.16.0:
    dependencies:
      reusify: 1.0.4

  faye-websocket@0.11.4:
    dependencies:
      websocket-driver: 0.7.4

  figures@3.2.0:
    dependencies:
      escape-string-regexp: 1.0.5

  fill-range@7.0.1:
    dependencies:
      to-regex-range: 5.0.1

  finalhandler@1.1.0:
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.3.0
      parseurl: 1.3.3
      statuses: 1.3.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  finalhandler@1.1.2:
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.3.0
      parseurl: 1.3.3
      statuses: 1.5.0
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  finalhandler@1.2.0:
    dependencies:
      debug: 2.6.9
      encodeurl: 1.0.2
      escape-html: 1.0.3
      on-finished: 2.4.1
      parseurl: 1.3.3
      statuses: 2.0.1
      unpipe: 1.0.0
    transitivePeerDependencies:
      - supports-color

  find-cache-dir@4.0.0:
    dependencies:
      common-path-prefix: 3.0.0
      pkg-dir: 7.0.0

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@6.3.0:
    dependencies:
      locate-path: 7.2.0
      path-exists: 5.0.0

  flatted@3.2.9: {}

  follow-redirects@1.15.3(debug@4.3.2):
    optionalDependencies:
      debug: 4.3.2

  foreground-child@3.1.1:
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 4.1.0

  form-data@3.0.1:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  forwarded@0.2.0: {}

  fraction.js@4.3.7: {}

  fresh@0.5.2: {}

  fs-extra@3.0.1:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 3.0.1
      universalify: 0.1.2

  fs-extra@8.1.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 4.0.0
      universalify: 0.1.2

  fs-minipass@2.1.0:
    dependencies:
      minipass: 3.3.6

  fs-minipass@3.0.3:
    dependencies:
      minipass: 7.0.4

  fs-monkey@1.0.5: {}

  fs.realpath@1.0.0: {}

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  gauge@4.0.4:
    dependencies:
      aproba: 2.0.0
      color-support: 1.1.3
      console-control-strings: 1.1.0
      has-unicode: 2.0.1
      signal-exit: 3.0.7
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wide-align: 1.1.5

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-intrinsic@1.2.2:
    dependencies:
      function-bind: 1.1.2
      has-proto: 1.0.1
      has-symbols: 1.0.3
      hasown: 2.0.0

  get-package-type@0.1.0: {}

  get-stream@6.0.1: {}

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob-to-regexp@0.4.1: {}

  glob@10.3.10:
    dependencies:
      foreground-child: 3.1.1
      jackspeak: 2.3.6
      minimatch: 9.0.3
      minipass: 7.0.4
      path-scurry: 1.10.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  glob@8.1.0:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 5.1.6
      once: 1.4.0

  globals@11.12.0: {}

  globby@13.2.2:
    dependencies:
      dir-glob: 3.0.1
      fast-glob: 3.3.1
      ignore: 5.3.0
      merge2: 1.4.1
      slash: 4.0.0

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.2

  graceful-fs@4.2.11: {}

  gsap@3.13.0: {}

  guess-parser@0.4.22(typescript@5.1.6):
    dependencies:
      '@wessberg/ts-evaluator': 0.0.27(typescript@5.1.6)
      typescript: 5.1.6
    transitivePeerDependencies:
      - bufferutil
      - canvas
      - supports-color
      - utf-8-validate

  handle-thing@2.0.1: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.1:
    dependencies:
      get-intrinsic: 1.2.2

  has-proto@1.0.1: {}

  has-symbols@1.0.3: {}

  has-unicode@2.0.1: {}

  hasown@2.0.0:
    dependencies:
      function-bind: 1.1.2

  hdr-histogram-js@2.0.3:
    dependencies:
      '@assemblyscript/loader': 0.10.1
      base64-js: 1.5.1
      pako: 1.0.11

  hdr-histogram-percentiles-obj@3.0.0: {}

  hosted-git-info@6.1.1:
    dependencies:
      lru-cache: 7.18.3

  hpack.js@2.1.6:
    dependencies:
      inherits: 2.0.4
      obuf: 1.1.2
      readable-stream: 2.3.8
      wbuf: 1.7.3

  html-encoding-sniffer@2.0.1:
    dependencies:
      whatwg-encoding: 1.0.5

  html-encoding-sniffer@3.0.0:
    dependencies:
      whatwg-encoding: 2.0.0

  html-entities@2.4.0: {}

  html-escaper@2.0.2: {}

  htmlparser2@8.0.2:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      domutils: 3.1.0
      entities: 4.5.0

  http-cache-semantics@4.1.1: {}

  http-deceiver@1.2.7: {}

  http-errors@1.6.3:
    dependencies:
      depd: 1.1.2
      inherits: 2.0.3
      setprototypeof: 1.1.0
      statuses: 1.5.0

  http-errors@2.0.0:
    dependencies:
      depd: 2.0.0
      inherits: 2.0.4
      setprototypeof: 1.2.0
      statuses: 2.0.1
      toidentifier: 1.0.1

  http-parser-js@0.5.8: {}

  http-proxy-agent@4.0.1:
    dependencies:
      '@tootallnate/once': 1.1.2
      agent-base: 6.0.2
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  http-proxy-agent@5.0.0:
    dependencies:
      '@tootallnate/once': 2.0.0
      agent-base: 6.0.2
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  http-proxy-middleware@2.0.6(@types/express@4.17.21):
    dependencies:
      '@types/http-proxy': 1.17.14
      http-proxy: 1.18.1
      is-glob: 4.0.3
      is-plain-obj: 3.0.0
      micromatch: 4.0.5
    optionalDependencies:
      '@types/express': 4.17.21
    transitivePeerDependencies:
      - debug

  http-proxy@1.18.1:
    dependencies:
      eventemitter3: 4.0.7
      follow-redirects: 1.15.3(debug@4.3.2)
      requires-port: 1.0.0
    transitivePeerDependencies:
      - debug

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  humanize-ms@1.2.1:
    dependencies:
      ms: 2.1.3

  iconv-lite@0.4.24:
    dependencies:
      safer-buffer: 2.1.2

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  icss-utils@5.1.0(postcss@8.4.31):
    dependencies:
      postcss: 8.4.31

  ieee754@1.2.1: {}

  ignore-walk@6.0.4:
    dependencies:
      minimatch: 9.0.3

  ignore@5.3.0: {}

  image-size@0.5.5:
    optional: true

  immutable@3.8.2: {}

  immutable@4.3.4: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  infer-owner@1.0.4: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.3: {}

  inherits@2.0.4: {}

  ini@4.1.1: {}

  inquirer@8.2.4:
    dependencies:
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-width: 3.0.0
      external-editor: 3.1.0
      figures: 3.2.0
      lodash: 4.17.21
      mute-stream: 0.0.8
      ora: 5.4.1
      run-async: 2.4.1
      rxjs: 7.8.1
      string-width: 4.2.3
      strip-ansi: 6.0.1
      through: 2.3.8
      wrap-ansi: 7.0.0

  ip@2.0.0: {}

  ipaddr.js@1.9.1: {}

  ipaddr.js@2.1.0: {}

  is-arrayish@0.2.1: {}

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.2.0

  is-core-module@2.13.1:
    dependencies:
      hasown: 2.0.0

  is-docker@2.2.1: {}

  is-extglob@2.1.1: {}

  is-fullwidth-code-point@3.0.0: {}

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-interactive@1.0.0: {}

  is-lambda@1.0.1: {}

  is-number-like@1.0.8:
    dependencies:
      lodash.isfinite: 3.3.2

  is-number@7.0.0: {}

  is-plain-obj@3.0.0: {}

  is-plain-object@2.0.4:
    dependencies:
      isobject: 3.0.1

  is-potential-custom-element-name@1.0.1: {}

  is-stream@2.0.1: {}

  is-unicode-supported@0.1.0: {}

  is-what@3.14.1: {}

  is-wsl@1.1.0: {}

  is-wsl@2.2.0:
    dependencies:
      is-docker: 2.2.1

  isarray@1.0.0: {}

  isbinaryfile@4.0.10: {}

  isexe@2.0.0: {}

  isobject@3.0.1: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-instrument@5.2.1:
    dependencies:
      '@babel/core': 7.23.6
      '@babel/parser': 7.23.6
      '@istanbuljs/schema': 0.1.3
      istanbul-lib-coverage: 3.2.2
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-lib-source-maps@4.0.1:
    dependencies:
      debug: 4.3.4
      istanbul-lib-coverage: 3.2.2
      source-map: 0.6.1
    transitivePeerDependencies:
      - supports-color

  istanbul-reports@3.1.6:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  jackspeak@2.3.6:
    dependencies:
      '@isaacs/cliui': 8.0.2
    optionalDependencies:
      '@pkgjs/parseargs': 0.11.0

  jasmine-core@4.6.0: {}

  jest-worker@27.5.1:
    dependencies:
      '@types/node': 16.18.97
      merge-stream: 2.0.0
      supports-color: 8.1.1

  jiti@1.21.0: {}

  js-tokens@4.0.0: {}

  js-yaml@3.14.1:
    dependencies:
      argparse: 1.0.10
      esprima: 4.0.1

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsdom@16.7.0:
    dependencies:
      abab: 2.0.6
      acorn: 8.11.2
      acorn-globals: 6.0.0
      cssom: 0.4.4
      cssstyle: 2.3.0
      data-urls: 2.0.0
      decimal.js: 10.4.3
      domexception: 2.0.1
      escodegen: 2.1.0
      form-data: 3.0.1
      html-encoding-sniffer: 2.0.1
      http-proxy-agent: 4.0.1
      https-proxy-agent: 5.0.1
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.7
      parse5: 6.0.1
      saxes: 5.0.1
      symbol-tree: 3.2.4
      tough-cookie: 4.1.3
      w3c-hr-time: 1.0.2
      w3c-xmlserializer: 2.0.0
      webidl-conversions: 6.1.0
      whatwg-encoding: 1.0.5
      whatwg-mimetype: 2.3.0
      whatwg-url: 8.7.0
      ws: 7.5.9
      xml-name-validator: 3.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsdom@22.1.0:
    dependencies:
      abab: 2.0.6
      cssstyle: 3.0.0
      data-urls: 4.0.0
      decimal.js: 10.4.3
      domexception: 4.0.0
      form-data: 4.0.0
      html-encoding-sniffer: 3.0.0
      http-proxy-agent: 5.0.0
      https-proxy-agent: 5.0.1
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.7
      parse5: 7.1.2
      rrweb-cssom: 0.6.0
      saxes: 6.0.0
      symbol-tree: 3.2.4
      tough-cookie: 4.1.3
      w3c-xmlserializer: 4.0.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 2.0.0
      whatwg-mimetype: 3.0.0
      whatwg-url: 12.0.1
      ws: 8.16.0
      xml-name-validator: 4.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@0.5.0: {}

  jsesc@2.5.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-parse-even-better-errors@3.0.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json5@2.2.3: {}

  jsonc-parser@3.2.0: {}

  jsonfile@3.0.1:
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonfile@4.0.0:
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonparse@1.3.1: {}

  karma-chrome-launcher@3.2.0:
    dependencies:
      which: 1.3.1

  karma-coverage@2.2.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-instrument: 5.2.1
      istanbul-lib-report: 3.0.1
      istanbul-lib-source-maps: 4.0.1
      istanbul-reports: 3.1.6
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  karma-jasmine-html-reporter@2.1.0(jasmine-core@4.6.0)(karma-jasmine@5.1.0(karma@6.4.2))(karma@6.4.2):
    dependencies:
      jasmine-core: 4.6.0
      karma: 6.4.2
      karma-jasmine: 5.1.0(karma@6.4.2)

  karma-jasmine@5.1.0(karma@6.4.2):
    dependencies:
      jasmine-core: 4.6.0
      karma: 6.4.2

  karma-source-map-support@1.4.0:
    dependencies:
      source-map-support: 0.5.21

  karma@6.4.2:
    dependencies:
      '@colors/colors': 1.5.0
      body-parser: 1.20.2
      braces: 3.0.2
      chokidar: 3.5.3
      connect: 3.7.0
      di: 0.0.1
      dom-serialize: 2.2.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      http-proxy: 1.18.1
      isbinaryfile: 4.0.10
      lodash: 4.17.21
      log4js: 6.9.1
      mime: 2.6.0
      minimatch: 3.1.2
      mkdirp: 0.5.6
      qjobs: 1.2.0
      range-parser: 1.2.1
      rimraf: 3.0.2
      socket.io: 4.7.2
      source-map: 0.6.1
      tmp: 0.2.1
      ua-parser-js: 0.7.37
      yargs: 16.2.0
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  kind-of@6.0.3: {}

  klona@2.0.6: {}

  launch-editor@2.6.1:
    dependencies:
      picocolors: 1.0.0
      shell-quote: 1.8.1

  less-loader@11.1.0(less@4.1.3)(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      klona: 2.0.6
      less: 4.1.3
      webpack: 5.88.2(esbuild@0.18.17)

  less@4.1.3:
    dependencies:
      copy-anything: 2.0.6
      parse-node-version: 1.0.1
      tslib: 2.6.2
    optionalDependencies:
      errno: 0.1.8
      graceful-fs: 4.2.11
      image-size: 0.5.5
      make-dir: 2.1.0
      mime: 1.6.0
      needle: 3.3.1
      source-map: 0.6.1

  license-webpack-plugin@4.0.2(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      webpack-sources: 3.2.3
    optionalDependencies:
      webpack: 5.88.2(esbuild@0.18.17)

  limiter@1.1.5: {}

  lines-and-columns@1.2.4: {}

  loader-runner@4.3.0: {}

  loader-utils@2.0.4:
    dependencies:
      big.js: 5.2.2
      emojis-list: 3.0.0
      json5: 2.2.3

  loader-utils@3.2.1: {}

  localtunnel@2.0.2:
    dependencies:
      axios: 0.21.4(debug@4.3.2)
      debug: 4.3.2
      openurl: 1.1.1
      yargs: 17.1.1
    transitivePeerDependencies:
      - supports-color

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@7.2.0:
    dependencies:
      p-locate: 6.0.0

  lodash-es@4.17.21: {}

  lodash.debounce@4.0.8: {}

  lodash.isfinite@3.3.2: {}

  lodash@4.17.21: {}

  log-symbols@4.1.0:
    dependencies:
      chalk: 4.1.2
      is-unicode-supported: 0.1.0

  log4js@6.9.1:
    dependencies:
      date-format: 4.0.14
      debug: 4.3.4
      flatted: 3.2.9
      rfdc: 1.3.0
      streamroller: 3.1.5
    transitivePeerDependencies:
      - supports-color

  lru-cache@10.1.0: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  lru-cache@7.18.3: {}

  magic-string@0.30.1:
    dependencies:
      '@jridgewell/sourcemap-codec': 1.4.15

  make-dir@2.1.0:
    dependencies:
      pify: 4.0.1
      semver: 5.7.2
    optional: true

  make-dir@4.0.0:
    dependencies:
      semver: 7.5.4

  make-fetch-happen@10.2.1:
    dependencies:
      agentkeepalive: 4.5.0
      cacache: 16.1.3
      http-cache-semantics: 4.1.1
      http-proxy-agent: 5.0.0
      https-proxy-agent: 5.0.1
      is-lambda: 1.0.1
      lru-cache: 7.18.3
      minipass: 3.3.6
      minipass-collect: 1.0.2
      minipass-fetch: 2.1.2
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      negotiator: 0.6.3
      promise-retry: 2.0.1
      socks-proxy-agent: 7.0.0
      ssri: 9.0.1
    transitivePeerDependencies:
      - bluebird
      - supports-color

  make-fetch-happen@11.1.1:
    dependencies:
      agentkeepalive: 4.5.0
      cacache: 17.1.4
      http-cache-semantics: 4.1.1
      http-proxy-agent: 5.0.0
      https-proxy-agent: 5.0.1
      is-lambda: 1.0.1
      lru-cache: 7.18.3
      minipass: 5.0.0
      minipass-fetch: 3.0.4
      minipass-flush: 1.0.5
      minipass-pipeline: 1.2.4
      negotiator: 0.6.3
      promise-retry: 2.0.1
      socks-proxy-agent: 7.0.0
      ssri: 10.0.5
    transitivePeerDependencies:
      - supports-color

  media-typer@0.3.0: {}

  memfs@3.5.3:
    dependencies:
      fs-monkey: 1.0.5

  merge-descriptors@1.0.1: {}

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  methods@1.1.2: {}

  micromatch@4.0.5:
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mime@1.4.1: {}

  mime@1.6.0: {}

  mime@2.6.0: {}

  mimic-fn@2.1.0: {}

  mini-css-extract-plugin@2.7.6(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      schema-utils: 4.2.0
      webpack: 5.88.2(esbuild@0.18.17)

  minimalistic-assert@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@5.1.6:
    dependencies:
      brace-expansion: 2.0.1

  minimatch@9.0.3:
    dependencies:
      brace-expansion: 2.0.1

  minimist@1.2.8: {}

  minipass-collect@1.0.2:
    dependencies:
      minipass: 3.3.6

  minipass-fetch@2.1.2:
    dependencies:
      minipass: 3.3.6
      minipass-sized: 1.0.3
      minizlib: 2.1.2
    optionalDependencies:
      encoding: 0.1.13

  minipass-fetch@3.0.4:
    dependencies:
      minipass: 7.0.4
      minipass-sized: 1.0.3
      minizlib: 2.1.2
    optionalDependencies:
      encoding: 0.1.13

  minipass-flush@1.0.5:
    dependencies:
      minipass: 3.3.6

  minipass-json-stream@1.0.1:
    dependencies:
      jsonparse: 1.3.1
      minipass: 3.3.6

  minipass-pipeline@1.2.4:
    dependencies:
      minipass: 3.3.6

  minipass-sized@1.0.3:
    dependencies:
      minipass: 3.3.6

  minipass@3.3.6:
    dependencies:
      yallist: 4.0.0

  minipass@5.0.0: {}

  minipass@7.0.4: {}

  minizlib@2.1.2:
    dependencies:
      minipass: 3.3.6
      yallist: 4.0.0

  mitt@1.2.0: {}

  mkdirp@0.5.6:
    dependencies:
      minimist: 1.2.8

  mkdirp@1.0.4: {}

  mrmime@1.0.1: {}

  ms@2.0.0: {}

  ms@2.1.2: {}

  ms@2.1.3: {}

  multicast-dns@7.2.5:
    dependencies:
      dns-packet: 5.6.1
      thunky: 1.1.0

  mute-stream@0.0.8: {}

  nanoid@3.3.7: {}

  needle@3.3.1:
    dependencies:
      iconv-lite: 0.6.3
      sax: 1.3.0
    optional: true

  negotiator@0.6.3: {}

  neo-async@2.6.2: {}

  ng2-charts@3.1.2(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(chart.js@3.9.1)(rxjs@7.8.1):
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      chart.js: 3.9.1
      lodash-es: 4.17.21
      rxjs: 7.8.1
      tslib: 2.6.2

  ngx-bootstrap@19.0.2(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/forms@16.2.12(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(rxjs@7.8.1))(rxjs@7.8.1):
    dependencies:
      '@angular/animations': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@angular/forms': 16.2.12(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(rxjs@7.8.1)
      rxjs: 7.8.1
      tslib: 2.6.2

  ngx-loading@17.0.0(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)):
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      tslib: 2.6.2

  ngx-pagination@6.0.3(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)):
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      tslib: 2.6.2

  ngx-toastr@19.0.0(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(@angular/platform-browser@16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))):
    dependencies:
      '@angular/common': 16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1)
      '@angular/core': 16.2.12(rxjs@7.8.1)(zone.js@0.13.3)
      '@angular/platform-browser': 16.2.12(@angular/animations@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3)))(@angular/common@16.2.12(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))(rxjs@7.8.1))(@angular/core@16.2.12(rxjs@7.8.1)(zone.js@0.13.3))
      tslib: 2.6.2

  nice-napi@1.0.2:
    dependencies:
      node-addon-api: 3.2.1
      node-gyp-build: 4.7.1
    optional: true

  node-addon-api@3.2.1:
    optional: true

  node-forge@1.3.1: {}

  node-gyp-build@4.7.1:
    optional: true

  node-gyp@9.4.1:
    dependencies:
      env-paths: 2.2.1
      exponential-backoff: 3.1.1
      glob: 7.2.3
      graceful-fs: 4.2.11
      make-fetch-happen: 10.2.1
      nopt: 6.0.0
      npmlog: 6.0.2
      rimraf: 3.0.2
      semver: 7.5.4
      tar: 6.2.0
      which: 2.0.2
    transitivePeerDependencies:
      - bluebird
      - supports-color

  node-releases@2.0.14: {}

  nopt@6.0.0:
    dependencies:
      abbrev: 1.1.1

  normalize-package-data@5.0.0:
    dependencies:
      hosted-git-info: 6.1.1
      is-core-module: 2.13.1
      semver: 7.5.4
      validate-npm-package-license: 3.0.4

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  npm-bundled@3.0.0:
    dependencies:
      npm-normalize-package-bin: 3.0.1

  npm-install-checks@6.3.0:
    dependencies:
      semver: 7.5.4

  npm-normalize-package-bin@3.0.1: {}

  npm-package-arg@10.1.0:
    dependencies:
      hosted-git-info: 6.1.1
      proc-log: 3.0.0
      semver: 7.5.4
      validate-npm-package-name: 5.0.0

  npm-packlist@7.0.4:
    dependencies:
      ignore-walk: 6.0.4

  npm-pick-manifest@8.0.1:
    dependencies:
      npm-install-checks: 6.3.0
      npm-normalize-package-bin: 3.0.1
      npm-package-arg: 10.1.0
      semver: 7.5.4

  npm-registry-fetch@14.0.5:
    dependencies:
      make-fetch-happen: 11.1.1
      minipass: 5.0.0
      minipass-fetch: 3.0.4
      minipass-json-stream: 1.0.1
      minizlib: 2.1.2
      npm-package-arg: 10.1.0
      proc-log: 3.0.0
    transitivePeerDependencies:
      - supports-color

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  npmlog@6.0.2:
    dependencies:
      are-we-there-yet: 3.0.1
      console-control-strings: 1.1.0
      gauge: 4.0.4
      set-blocking: 2.0.0

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  nwsapi@2.2.7: {}

  object-assign@4.1.1: {}

  object-inspect@1.13.1: {}

  object-path@0.11.8: {}

  obuf@1.1.2: {}

  on-finished@2.3.0:
    dependencies:
      ee-first: 1.1.1

  on-finished@2.4.1:
    dependencies:
      ee-first: 1.1.1

  on-headers@1.0.2: {}

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  open@8.4.2:
    dependencies:
      define-lazy-prop: 2.0.0
      is-docker: 2.2.1
      is-wsl: 2.2.0

  openurl@1.1.1: {}

  opn@5.3.0:
    dependencies:
      is-wsl: 1.1.0

  ora@5.4.1:
    dependencies:
      bl: 4.1.0
      chalk: 4.1.2
      cli-cursor: 3.1.0
      cli-spinners: 2.9.2
      is-interactive: 1.0.0
      is-unicode-supported: 0.1.0
      log-symbols: 4.1.0
      strip-ansi: 6.0.1
      wcwidth: 1.0.1

  os-tmpdir@1.0.2: {}

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@4.0.0:
    dependencies:
      yocto-queue: 1.0.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@6.0.0:
    dependencies:
      p-limit: 4.0.0

  p-map@4.0.0:
    dependencies:
      aggregate-error: 3.1.0

  p-retry@4.6.2:
    dependencies:
      '@types/retry': 0.12.0
      retry: 0.13.1

  p-try@2.2.0: {}

  pacote@15.2.0:
    dependencies:
      '@npmcli/git': 4.1.0
      '@npmcli/installed-package-contents': 2.0.2
      '@npmcli/promise-spawn': 6.0.2
      '@npmcli/run-script': 6.0.2
      cacache: 17.1.4
      fs-minipass: 3.0.3
      minipass: 5.0.0
      npm-package-arg: 10.1.0
      npm-packlist: 7.0.4
      npm-pick-manifest: 8.0.1
      npm-registry-fetch: 14.0.5
      proc-log: 3.0.0
      promise-retry: 2.0.1
      read-package-json: 6.0.4
      read-package-json-fast: 3.0.2
      sigstore: 1.9.0
      ssri: 10.0.5
      tar: 6.2.0
    transitivePeerDependencies:
      - bluebird
      - supports-color

  pako@1.0.11: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@5.2.0:
    dependencies:
      '@babel/code-frame': 7.23.5
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse-node-version@1.0.1: {}

  parse5-html-rewriting-stream@7.0.0:
    dependencies:
      entities: 4.5.0
      parse5: 7.1.2
      parse5-sax-parser: 7.0.0

  parse5-sax-parser@7.0.0:
    dependencies:
      parse5: 7.1.2

  parse5@6.0.1: {}

  parse5@7.1.2:
    dependencies:
      entities: 4.5.0

  parseurl@1.3.3: {}

  path-exists@4.0.0: {}

  path-exists@5.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@3.1.1: {}

  path-parse@1.0.7: {}

  path-scurry@1.10.1:
    dependencies:
      lru-cache: 10.1.0
      minipass: 7.0.4

  path-to-regexp@0.1.7: {}

  path-type@4.0.0: {}

  picocolors@1.0.0: {}

  picomatch@2.3.1: {}

  pify@4.0.1:
    optional: true

  piscina@4.0.0:
    dependencies:
      eventemitter-asyncresource: 1.0.0
      hdr-histogram-js: 2.0.3
      hdr-histogram-percentiles-obj: 3.0.0
    optionalDependencies:
      nice-napi: 1.0.2

  pkg-dir@7.0.0:
    dependencies:
      find-up: 6.3.0

  portscanner@2.2.0:
    dependencies:
      async: 2.6.4
      is-number-like: 1.0.8

  positioning@2.0.1: {}

  postcss-loader@7.3.3(postcss@8.4.31)(typescript@5.1.6)(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      cosmiconfig: 8.3.6(typescript@5.1.6)
      jiti: 1.21.0
      postcss: 8.4.31
      semver: 7.5.4
      webpack: 5.88.2(esbuild@0.18.17)
    transitivePeerDependencies:
      - typescript

  postcss-modules-extract-imports@3.0.0(postcss@8.4.31):
    dependencies:
      postcss: 8.4.31

  postcss-modules-local-by-default@4.0.3(postcss@8.4.31):
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.31)
      postcss: 8.4.31
      postcss-selector-parser: 6.0.14
      postcss-value-parser: 4.2.0

  postcss-modules-scope@3.1.0(postcss@8.4.31):
    dependencies:
      postcss: 8.4.31
      postcss-selector-parser: 6.0.14

  postcss-modules-values@4.0.0(postcss@8.4.31):
    dependencies:
      icss-utils: 5.1.0(postcss@8.4.31)
      postcss: 8.4.31

  postcss-selector-parser@6.0.14:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.4.31:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.0
      source-map-js: 1.0.2

  pretty-bytes@5.6.0: {}

  proc-log@3.0.0: {}

  process-nextick-args@2.0.1: {}

  promise-inflight@1.0.1: {}

  promise-retry@2.0.1:
    dependencies:
      err-code: 2.0.3
      retry: 0.12.0

  proxy-addr@2.0.7:
    dependencies:
      forwarded: 0.2.0
      ipaddr.js: 1.9.1

  prr@1.0.1:
    optional: true

  psl@1.9.0: {}

  punycode@2.3.1: {}

  qjobs@1.2.0: {}

  qs@6.11.0:
    dependencies:
      side-channel: 1.0.4

  querystringify@2.2.0: {}

  queue-microtask@1.2.3: {}

  randombytes@2.1.0:
    dependencies:
      safe-buffer: 5.2.1

  range-parser@1.2.1: {}

  raw-body@2.5.1:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  raw-body@2.5.2:
    dependencies:
      bytes: 3.1.2
      http-errors: 2.0.0
      iconv-lite: 0.4.24
      unpipe: 1.0.0

  read-package-json-fast@3.0.2:
    dependencies:
      json-parse-even-better-errors: 3.0.1
      npm-normalize-package-bin: 3.0.1

  read-package-json@6.0.4:
    dependencies:
      glob: 10.3.10
      json-parse-even-better-errors: 3.0.1
      normalize-package-data: 5.0.0
      npm-normalize-package-bin: 3.0.1

  readable-stream@2.3.8:
    dependencies:
      core-util-is: 1.0.3
      inherits: 2.0.4
      isarray: 1.0.0
      process-nextick-args: 2.0.1
      safe-buffer: 5.1.2
      string_decoder: 1.1.1
      util-deprecate: 1.0.2

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  reflect-metadata@0.1.14: {}

  regenerate-unicode-properties@10.1.1:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.13.11: {}

  regenerator-transform@0.15.2:
    dependencies:
      '@babel/runtime': 7.22.6

  regex-parser@2.2.11: {}

  regexpu-core@5.3.2:
    dependencies:
      '@babel/regjsgen': 0.8.0
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.1.1
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.1.0

  regjsparser@0.9.1:
    dependencies:
      jsesc: 0.5.0

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  requires-port@1.0.0: {}

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve-url-loader@5.0.0:
    dependencies:
      adjust-sourcemap-loader: 4.0.0
      convert-source-map: 1.9.0
      loader-utils: 2.0.4
      postcss: 8.4.31
      source-map: 0.6.1

  resolve@1.22.2:
    dependencies:
      is-core-module: 2.13.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.13.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resp-modifier@6.0.2:
    dependencies:
      debug: 2.6.9
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  restore-cursor@3.1.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  retry@0.12.0: {}

  retry@0.13.1: {}

  reusify@1.0.4: {}

  rfdc@1.3.0: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rollup@3.29.4:
    optionalDependencies:
      fsevents: 2.3.3

  rrweb-cssom@0.6.0: {}

  run-async@2.4.1: {}

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  rx@4.1.0: {}

  rxjs@7.8.1:
    dependencies:
      tslib: 2.6.2

  safe-buffer@5.1.2: {}

  safe-buffer@5.2.1: {}

  safer-buffer@2.1.2: {}

  safevalues@0.3.4: {}

  sass-loader@13.3.2(sass@1.64.1)(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      neo-async: 2.6.2
      webpack: 5.88.2(esbuild@0.18.17)
    optionalDependencies:
      sass: 1.64.1

  sass@1.64.1:
    dependencies:
      chokidar: 3.5.3
      immutable: 4.3.4
      source-map-js: 1.0.2

  sax@1.3.0:
    optional: true

  saxes@5.0.1:
    dependencies:
      xmlchars: 2.2.0

  saxes@6.0.0:
    dependencies:
      xmlchars: 2.2.0

  schema-utils@3.3.0:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 6.12.6
      ajv-keywords: 3.5.2(ajv@6.12.6)

  schema-utils@4.2.0:
    dependencies:
      '@types/json-schema': 7.0.15
      ajv: 8.12.0
      ajv-formats: 2.1.1(ajv@8.12.0)
      ajv-keywords: 5.1.0(ajv@8.12.0)

  select-hose@2.0.0: {}

  selfsigned@2.4.1:
    dependencies:
      '@types/node-forge': 1.3.10
      node-forge: 1.3.1

  semver@5.7.2:
    optional: true

  semver@6.3.1: {}

  semver@7.5.4:
    dependencies:
      lru-cache: 6.0.0

  send@0.16.2:
    dependencies:
      debug: 2.6.9
      depd: 1.1.2
      destroy: 1.0.4
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 1.6.3
      mime: 1.4.1
      ms: 2.0.0
      on-finished: 2.3.0
      range-parser: 1.2.1
      statuses: 1.4.0
    transitivePeerDependencies:
      - supports-color

  send@0.18.0:
    dependencies:
      debug: 2.6.9
      depd: 2.0.0
      destroy: 1.2.0
      encodeurl: 1.0.2
      escape-html: 1.0.3
      etag: 1.8.1
      fresh: 0.5.2
      http-errors: 2.0.0
      mime: 1.6.0
      ms: 2.1.3
      on-finished: 2.4.1
      range-parser: 1.2.1
      statuses: 2.0.1
    transitivePeerDependencies:
      - supports-color

  serialize-javascript@6.0.1:
    dependencies:
      randombytes: 2.1.0

  serve-index@1.9.1:
    dependencies:
      accepts: 1.3.8
      batch: 0.6.1
      debug: 2.6.9
      escape-html: 1.0.3
      http-errors: 1.6.3
      mime-types: 2.1.35
      parseurl: 1.3.3
    transitivePeerDependencies:
      - supports-color

  serve-static@1.13.2:
    dependencies:
      encodeurl: 1.0.2
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.16.2
    transitivePeerDependencies:
      - supports-color

  serve-static@1.15.0:
    dependencies:
      encodeurl: 1.0.2
      escape-html: 1.0.3
      parseurl: 1.3.3
      send: 0.18.0
    transitivePeerDependencies:
      - supports-color

  server-destroy@1.0.1: {}

  set-blocking@2.0.0: {}

  set-function-length@1.1.1:
    dependencies:
      define-data-property: 1.1.1
      get-intrinsic: 1.2.2
      gopd: 1.0.1
      has-property-descriptors: 1.0.1

  setprototypeof@1.1.0: {}

  setprototypeof@1.2.0: {}

  shallow-clone@3.0.1:
    dependencies:
      kind-of: 6.0.3

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@3.0.0: {}

  shell-quote@1.8.1: {}

  side-channel@1.0.4:
    dependencies:
      call-bind: 1.0.5
      get-intrinsic: 1.2.2
      object-inspect: 1.13.1

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  sigstore@1.9.0:
    dependencies:
      '@sigstore/bundle': 1.1.0
      '@sigstore/protobuf-specs': 0.2.1
      '@sigstore/sign': 1.0.0
      '@sigstore/tuf': 1.0.3
      make-fetch-happen: 11.1.1
    transitivePeerDependencies:
      - supports-color

  slash@4.0.0: {}

  smart-buffer@4.2.0: {}

  socket.io-adapter@2.5.2:
    dependencies:
      ws: 8.11.0
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate

  socket.io-client@4.7.5:
    dependencies:
      '@socket.io/component-emitter': 3.1.0
      debug: 4.3.4
      engine.io-client: 6.5.3
      socket.io-parser: 4.2.4
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  socket.io-parser@4.2.4:
    dependencies:
      '@socket.io/component-emitter': 3.1.0
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  socket.io@4.7.2:
    dependencies:
      accepts: 1.3.8
      base64id: 2.0.0
      cors: 2.8.5
      debug: 4.3.4
      engine.io: 6.5.4
      socket.io-adapter: 2.5.2
      socket.io-parser: 4.2.4
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  sockjs@0.3.24:
    dependencies:
      faye-websocket: 0.11.4
      uuid: 8.3.2
      websocket-driver: 0.7.4

  socks-proxy-agent@7.0.0:
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.4
      socks: 2.7.1
    transitivePeerDependencies:
      - supports-color

  socks@2.7.1:
    dependencies:
      ip: 2.0.0
      smart-buffer: 4.2.0

  source-map-js@1.0.2: {}

  source-map-loader@4.0.1(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      abab: 2.0.6
      iconv-lite: 0.6.3
      source-map-js: 1.0.2
      webpack: 5.88.2(esbuild@0.18.17)

  source-map-support@0.5.21:
    dependencies:
      buffer-from: 1.1.2
      source-map: 0.6.1

  source-map@0.6.1: {}

  source-map@0.7.4: {}

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.16

  spdx-exceptions@2.3.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.3.0
      spdx-license-ids: 3.0.16

  spdx-license-ids@3.0.16: {}

  spdy-transport@3.0.0:
    dependencies:
      debug: 4.3.4
      detect-node: 2.1.0
      hpack.js: 2.1.6
      obuf: 1.1.2
      readable-stream: 3.6.2
      wbuf: 1.7.3
    transitivePeerDependencies:
      - supports-color

  spdy@4.0.2:
    dependencies:
      debug: 4.3.4
      handle-thing: 2.0.1
      http-deceiver: 1.2.7
      select-hose: 2.0.0
      spdy-transport: 3.0.0
    transitivePeerDependencies:
      - supports-color

  sprintf-js@1.0.3: {}

  ssri@10.0.5:
    dependencies:
      minipass: 7.0.4

  ssri@9.0.1:
    dependencies:
      minipass: 3.3.6

  statuses@1.3.1: {}

  statuses@1.4.0: {}

  statuses@1.5.0: {}

  statuses@2.0.1: {}

  stream-throttle@0.1.3:
    dependencies:
      commander: 2.20.3
      limiter: 1.1.5

  streamroller@3.1.5:
    dependencies:
      date-format: 4.0.14
      debug: 4.3.4
      fs-extra: 8.1.0
    transitivePeerDependencies:
      - supports-color

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string_decoder@1.1.1:
    dependencies:
      safe-buffer: 5.1.2

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.0.1

  strip-final-newline@2.0.0: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-color@8.1.1:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  symbol-observable@4.0.0: {}

  symbol-tree@3.2.4: {}

  tapable@2.2.1: {}

  tar@6.2.0:
    dependencies:
      chownr: 2.0.0
      fs-minipass: 2.1.0
      minipass: 5.0.0
      minizlib: 2.1.2
      mkdirp: 1.0.4
      yallist: 4.0.0

  terser-webpack-plugin@5.3.9(esbuild@0.18.17)(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      '@jridgewell/trace-mapping': 0.3.20
      jest-worker: 27.5.1
      schema-utils: 3.3.0
      serialize-javascript: 6.0.1
      terser: 5.19.2
      webpack: 5.88.2(esbuild@0.18.17)
    optionalDependencies:
      esbuild: 0.18.17

  terser@5.19.2:
    dependencies:
      '@jridgewell/source-map': 0.3.5
      acorn: 8.11.2
      commander: 2.20.3
      source-map-support: 0.5.21

  test-exclude@6.0.0:
    dependencies:
      '@istanbuljs/schema': 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  text-table@0.2.0: {}

  through@2.3.8: {}

  thunky@1.1.0: {}

  tmp@0.0.33:
    dependencies:
      os-tmpdir: 1.0.2

  tmp@0.2.1:
    dependencies:
      rimraf: 3.0.2

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toidentifier@1.0.1: {}

  tough-cookie@4.1.3:
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10

  tr46@2.1.0:
    dependencies:
      punycode: 2.3.1

  tr46@4.1.1:
    dependencies:
      punycode: 2.3.1

  tree-kill@1.2.2: {}

  tslib@2.6.1: {}

  tslib@2.6.2: {}

  tuf-js@1.1.7:
    dependencies:
      '@tufjs/models': 1.0.4
      debug: 4.3.4
      make-fetch-happen: 11.1.1
    transitivePeerDependencies:
      - supports-color

  type-fest@0.21.3: {}

  type-is@1.6.18:
    dependencies:
      media-typer: 0.3.0
      mime-types: 2.1.35

  typed-assert@1.0.9: {}

  typescript@5.1.6: {}

  ua-parser-js@0.7.37: {}

  ua-parser-js@1.0.37: {}

  unicode-canonical-property-names-ecmascript@2.0.0: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.0
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.1.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  unique-filename@2.0.1:
    dependencies:
      unique-slug: 3.0.0

  unique-filename@3.0.0:
    dependencies:
      unique-slug: 4.0.0

  unique-slug@3.0.0:
    dependencies:
      imurmurhash: 0.1.4

  unique-slug@4.0.0:
    dependencies:
      imurmurhash: 0.1.4

  universalify@0.1.2: {}

  universalify@0.2.0: {}

  unpipe@1.0.0: {}

  update-browserslist-db@1.0.13(browserslist@4.22.2):
    dependencies:
      browserslist: 4.22.2
      escalade: 3.1.1
      picocolors: 1.0.0

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  util-deprecate@1.0.2: {}

  utils-merge@1.0.1: {}

  uuid@8.3.2: {}

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  validate-npm-package-name@5.0.0:
    dependencies:
      builtins: 5.0.1

  vary@1.1.2: {}

  vite@4.5.3(@types/node@16.18.97)(less@4.1.3)(sass@1.64.1)(terser@5.19.2):
    dependencies:
      esbuild: 0.18.17
      postcss: 8.4.31
      rollup: 3.29.4
    optionalDependencies:
      '@types/node': 16.18.97
      fsevents: 2.3.3
      less: 4.1.3
      sass: 1.64.1
      terser: 5.19.2

  void-elements@2.0.1: {}

  w3c-hr-time@1.0.2:
    dependencies:
      browser-process-hrtime: 1.0.0

  w3c-xmlserializer@2.0.0:
    dependencies:
      xml-name-validator: 3.0.0

  w3c-xmlserializer@4.0.0:
    dependencies:
      xml-name-validator: 4.0.0

  watchpack@2.4.0:
    dependencies:
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11

  wbuf@1.7.3:
    dependencies:
      minimalistic-assert: 1.0.1

  wcwidth@1.0.1:
    dependencies:
      defaults: 1.0.4

  webidl-conversions@5.0.0: {}

  webidl-conversions@6.1.0: {}

  webidl-conversions@7.0.0: {}

  webpack-dev-middleware@5.3.3(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      colorette: 2.0.20
      memfs: 3.5.3
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.2.0
      webpack: 5.88.2(esbuild@0.18.17)

  webpack-dev-middleware@6.1.2(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      colorette: 2.0.20
      memfs: 3.5.3
      mime-types: 2.1.35
      range-parser: 1.2.1
      schema-utils: 4.2.0
    optionalDependencies:
      webpack: 5.88.2(esbuild@0.18.17)

  webpack-dev-server@4.15.1(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      '@types/bonjour': 3.5.13
      '@types/connect-history-api-fallback': 1.5.4
      '@types/express': 4.17.21
      '@types/serve-index': 1.9.4
      '@types/serve-static': 1.15.5
      '@types/sockjs': 0.3.36
      '@types/ws': 8.5.10
      ansi-html-community: 0.0.8
      bonjour-service: 1.1.1
      chokidar: 3.5.3
      colorette: 2.0.20
      compression: 1.7.4
      connect-history-api-fallback: 2.0.0
      default-gateway: 6.0.3
      express: 4.18.2
      graceful-fs: 4.2.11
      html-entities: 2.4.0
      http-proxy-middleware: 2.0.6(@types/express@4.17.21)
      ipaddr.js: 2.1.0
      launch-editor: 2.6.1
      open: 8.4.2
      p-retry: 4.6.2
      rimraf: 3.0.2
      schema-utils: 4.2.0
      selfsigned: 2.4.1
      serve-index: 1.9.1
      sockjs: 0.3.24
      spdy: 4.0.2
      webpack-dev-middleware: 5.3.3(webpack@5.88.2(esbuild@0.18.17))
      ws: 8.16.0
    optionalDependencies:
      webpack: 5.88.2(esbuild@0.18.17)
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate

  webpack-merge@5.9.0:
    dependencies:
      clone-deep: 4.0.1
      wildcard: 2.0.1

  webpack-sources@3.2.3: {}

  webpack-subresource-integrity@5.1.0(webpack@5.88.2(esbuild@0.18.17)):
    dependencies:
      typed-assert: 1.0.9
      webpack: 5.88.2(esbuild@0.18.17)

  webpack@5.88.2(esbuild@0.18.17):
    dependencies:
      '@types/eslint-scope': 3.7.7
      '@types/estree': 1.0.5
      '@webassemblyjs/ast': 1.11.6
      '@webassemblyjs/wasm-edit': 1.11.6
      '@webassemblyjs/wasm-parser': 1.11.6
      acorn: 8.11.2
      acorn-import-assertions: 1.9.0(acorn@8.11.2)
      browserslist: 4.22.2
      chrome-trace-event: 1.0.3
      enhanced-resolve: 5.15.0
      es-module-lexer: 1.4.1
      eslint-scope: 5.1.1
      events: 3.3.0
      glob-to-regexp: 0.4.1
      graceful-fs: 4.2.11
      json-parse-even-better-errors: 2.3.1
      loader-runner: 4.3.0
      mime-types: 2.1.35
      neo-async: 2.6.2
      schema-utils: 3.3.0
      tapable: 2.2.1
      terser-webpack-plugin: 5.3.9(esbuild@0.18.17)(webpack@5.88.2(esbuild@0.18.17))
      watchpack: 2.4.0
      webpack-sources: 3.2.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js

  websocket-driver@0.7.4:
    dependencies:
      http-parser-js: 0.5.8
      safe-buffer: 5.2.1
      websocket-extensions: 0.1.4

  websocket-extensions@0.1.4: {}

  whatwg-encoding@1.0.5:
    dependencies:
      iconv-lite: 0.4.24

  whatwg-encoding@2.0.0:
    dependencies:
      iconv-lite: 0.6.3

  whatwg-mimetype@2.3.0: {}

  whatwg-mimetype@3.0.0: {}

  whatwg-url@12.0.1:
    dependencies:
      tr46: 4.1.1
      webidl-conversions: 7.0.0

  whatwg-url@8.7.0:
    dependencies:
      lodash: 4.17.21
      tr46: 2.1.0
      webidl-conversions: 6.1.0

  which@1.3.1:
    dependencies:
      isexe: 2.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  which@3.0.1:
    dependencies:
      isexe: 2.0.0

  wide-align@1.1.5:
    dependencies:
      string-width: 4.2.3

  wildcard@2.0.1: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  ws@7.5.9: {}

  ws@8.11.0: {}

  ws@8.16.0: {}

  xhr2@0.2.1: {}

  xml-name-validator@3.0.0: {}

  xml-name-validator@4.0.0: {}

  xmlchars@2.2.0: {}

  xmlhttprequest-ssl@2.0.0: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yargs-parser@20.2.9: {}

  yargs-parser@21.1.1: {}

  yargs@16.2.0:
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  yargs@17.1.1:
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.1.1
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yocto-queue@1.0.0: {}

  zone.js@0.13.3:
    dependencies:
      tslib: 2.6.2
