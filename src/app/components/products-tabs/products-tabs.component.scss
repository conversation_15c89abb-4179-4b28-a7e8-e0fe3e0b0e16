/* === SECTION PRODUITS AVEC ONGLETS - STYLE SEAMLESSHR === */
.products-section {
  padding: 80px 0;
  background: var(--white);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 16px;

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
}

/* === ONGLETS DE NAVIGATION === */
.products-tabs {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 60px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: 4px;
    margin-bottom: 40px;
  }

  .tab-button {
    padding: 12px 24px;
    background: transparent;
    border: 2px solid var(--gray-200);
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-600);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--gray-300);
      color: var(--gray-700);
    }

    &.active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-color: transparent;
      color: var(--white);
    }

    @media (max-width: 768px) {
      padding: 10px 16px;
      font-size: 0.875rem;
    }

    @media (max-width: 480px) {
      padding: 8px 12px;
      font-size: 0.8rem;
    }
  }
}

/* === CONTENU DES ONGLETS === */
.tab-content {
  .tab-panel {
    display: none;

    &.active {
      display: block;
    }
  }

  .product-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }
  }

  .product-text {
    .product-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--gray-900);
      margin-bottom: 20px;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 1.75rem;
      }
    }

    .product-description {
      font-size: 1.125rem;
      line-height: 1.6;
      color: var(--gray-600);
      margin-bottom: 24px;
    }

    .product-note {
      font-size: 0.875rem;
      color: var(--gray-500);
      font-style: italic;
      margin-bottom: 24px;
    }

    .learn-more-btn {
      display: inline-block;
      color: #667eea;
      font-weight: 600;
      text-decoration: none;
      font-size: 1.125rem;
      transition: all 0.3s ease;

      &:hover {
        color: #764ba2;
        text-decoration: underline;
      }
    }
  }

  .product-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    .product-image {
      width: 100%;
      max-width: 500px;
      height: auto;
      border-radius: 12px;
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid var(--gray-200);
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .products-section {
    padding: 60px 0;
  }

  .section-header {
    margin-bottom: 40px;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
