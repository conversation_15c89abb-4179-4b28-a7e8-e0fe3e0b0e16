/* === SECTION PRODUITS - STYLE TWENTY.COM (ÉPURÉ ET MODERNE) === */
.twenty-products {
  padding: 120px 0;
  background: var(--gray-50);
}

.section-header {
  text-align: center;
  margin-bottom: 80px;

  .section-title {
    font-size: 5rem;
    font-weight: 900;
    line-height: 0.85;
    color: var(--gray-700);
    margin-bottom: 24px;
    letter-spacing: -0.04em;

    @media (max-width: 1024px) {
      font-size: 4rem;
    }

    @media (max-width: 768px) {
      font-size: 3rem;
    }

    .title-highlight {
      color: var(--gray-900);
    }
  }

  .section-subtitle {
    font-size: 1.25rem;
    color: var(--gray-600);
    max-width: 500px;
    margin: 0 auto;
    line-height: 1.5;

    @media (max-width: 768px) {
      font-size: 1.125rem;
    }
  }
}

/* === ONGLETS DE NAVIGATION - STYLE TWENTY === */
.products-tabs {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 80px;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    gap: 8px;
    margin-bottom: 60px;
  }

  .tab-button {
    padding: 16px 32px;
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    color: var(--gray-700);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }

    &:hover {
      border-color: var(--gray-300);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);

      &::before {
        left: 100%;
      }
    }

    &.active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-color: transparent;
      color: var(--white);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    @media (max-width: 768px) {
      padding: 12px 24px;
      font-size: 0.875rem;
    }

    @media (max-width: 480px) {
      padding: 10px 20px;
      font-size: 0.8rem;
    }
  }
}

/* === CONTENU DES ONGLETS === */
.tab-content {
  .tab-panel {
    display: none;

    &.active {
      display: block;
    }
  }

  .product-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 40px;
      text-align: center;
    }
  }

  .product-text {
    .product-title {
      font-size: 2rem;
      font-weight: 700;
      color: var(--gray-900);
      margin-bottom: 20px;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 1.75rem;
      }
    }

    .product-description {
      font-size: 1.125rem;
      line-height: 1.6;
      color: var(--gray-600);
      margin-bottom: 24px;
    }

    .product-note {
      font-size: 0.875rem;
      color: var(--gray-500);
      font-style: italic;
      margin-bottom: 24px;
    }

    .learn-more-btn {
      display: inline-block;
      color: #667eea;
      font-weight: 600;
      text-decoration: none;
      font-size: 1.125rem;
      transition: all 0.3s ease;

      &:hover {
        color: #764ba2;
        text-decoration: underline;
      }
    }
  }

  .product-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    .product-image {
      width: 100%;
      max-width: 500px;
      height: auto;
      border-radius: 12px;
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
      border: 1px solid var(--gray-200);
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .products-section {
    padding: 60px 0;
  }

  .section-header {
    margin-bottom: 40px;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
