/* === SECTION TÉMOIGNAGES CLIENTS - STYLE SEAMLESSHR === */
.customer-stories-section {
  padding: 80px 0;
  background: var(--gray-50);
}

.section-header {
  text-align: center;
  margin-bottom: 60px;

  .section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-900);
    max-width: 800px;
    margin: 0 auto;
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }
}

/* === CARROUSEL DE TÉMOIGNAGES === */
.testimonials-carousel {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  margin-bottom: 40px;

  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .testimonial-card {
    background: var(--white);
    padding: 32px;
    border-radius: 16px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    text-align: center;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .company-logo {
      margin-bottom: 24px;

      .client-logo {
        height: 60px;
        width: auto;
        opacity: 0.8;
      }
    }

    .company-name {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--gray-900);
      margin-bottom: 20px;
      line-height: 1.4;
    }

    .read-story-btn {
      color: #667eea;
      font-weight: 600;
      text-decoration: none;
      font-size: 1rem;
      transition: all 0.3s ease;

      &:hover {
        color: #764ba2;
        text-decoration: underline;
      }
    }
  }
}

/* === NAVIGATION DU CARROUSEL === */
.carousel-navigation {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;

  .nav-btn {
    width: 48px;
    height: 48px;
    background: var(--white);
    border: 2px solid var(--gray-200);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: var(--gray-600);

    &:hover {
      border-color: #667eea;
      color: #667eea;
      transform: scale(1.05);
    }

    svg {
      width: 20px;
      height: 20px;
    }
  }
}

/* === INDICATEURS DE PAGINATION === */
.carousel-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;

  .indicator {
    width: 12px;
    height: 12px;
    background: var(--gray-300);
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      background: #667eea;
      transform: scale(1.2);
    }

    &:hover {
      background: #667eea;
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .customer-stories-section {
    padding: 60px 0;
  }

  .section-header {
    margin-bottom: 40px;
  }

  .testimonials-carousel {
    margin-bottom: 32px;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
