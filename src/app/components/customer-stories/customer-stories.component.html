<!-- SECTION TÉMOIGNAGES CLIENTS - EXACTEMENT COMME SEAMLESSHR -->
<section class="customer-stories-section">
  <div class="container">
    <!-- En-tête de section -->
    <div class="section-header">
      <h2 class="section-title">
        Nous aidons des entreprises comme la vôtre à devenir meilleures, plus
        intelligentes et plus fluides
      </h2>
    </div>

    <!-- Carrousel de témoignages -->
    <div class="testimonials-carousel">
      <div class="testimonial-card active">
        <div class="company-logo">
          <img
            src="assets/images/clients/airbus.png"
            alt="Airbus"
            class="client-logo"
          />
        </div>
        <h3 class="company-name">Airbus France centralise ses opérations RH</h3>
        <a href="#" class="read-story-btn">Lire l'histoire</a>
      </div>

      <div class="testimonial-card">
        <div class="company-logo">
          <img
            src="assets/images/clients/orange.png"
            alt="Orange"
            class="client-logo"
          />
        </div>
        <h3 class="company-name">
          Orange améliore ses opérations RH globales avec CompulseHR
        </h3>
        <a href="#" class="read-story-btn">Lire l'histoire</a>
      </div>

      <div class="testimonial-card">
        <div class="company-logo">
          <img
            src="assets/images/clients/bnp.png"
            alt="BNP Paribas"
            class="client-logo"
          />
        </div>
        <h3 class="company-name">
          BNP Paribas améliore l'analytique et le reporting RH avec CompulseHR
        </h3>
        <a href="#" class="read-story-btn">Lire l'histoire</a>
      </div>

      <div class="testimonial-card">
        <div class="company-logo">
          <img
            src="assets/images/clients/sncf.png"
            alt="SNCF"
            class="client-logo"
          />
        </div>
        <h3 class="company-name">
          SNCF améliore l'efficacité et la précision avec CompulseHR
        </h3>
        <a href="#" class="read-story-btn">Lire l'histoire</a>
      </div>

      <div class="testimonial-card">
        <div class="company-logo">
          <img
            src="assets/images/clients/total.png"
            alt="TotalEnergies"
            class="client-logo"
          />
        </div>
        <h3 class="company-name">
          TotalEnergies gère une paie conforme et booste la productivité avec
          CompulseHR
        </h3>
        <a href="#" class="read-story-btn">Lire l'histoire</a>
      </div>

      <div class="testimonial-card">
        <div class="company-logo">
          <img
            src="assets/images/clients/carrefour.png"
            alt="Carrefour"
            class="client-logo"
          />
        </div>
        <h3 class="company-name">
          Carrefour booste l'efficacité opérationnelle avec CompulseHR
        </h3>
        <a href="#" class="read-story-btn">Lire l'histoire</a>
      </div>
    </div>

    <!-- Navigation du carrousel -->
    <div class="carousel-navigation">
      <button class="nav-btn prev-btn" (click)="previousSlide()">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path
            d="M15 18l-6-6 6-6"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
      <button class="nav-btn next-btn" (click)="nextSlide()">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path
            d="M9 18l6-6-6-6"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </button>
    </div>

    <!-- Indicateurs de pagination -->
    <div class="carousel-indicators">
      <button
        class="indicator"
        [class.active]="currentSlide === 0"
        (click)="goToSlide(0)"
      ></button>
      <button
        class="indicator"
        [class.active]="currentSlide === 1"
        (click)="goToSlide(1)"
      ></button>
      <button
        class="indicator"
        [class.active]="currentSlide === 2"
        (click)="goToSlide(2)"
      ></button>
      <button
        class="indicator"
        [class.active]="currentSlide === 3"
        (click)="goToSlide(3)"
      ></button>
      <button
        class="indicator"
        [class.active]="currentSlide === 4"
        (click)="goToSlide(4)"
      ></button>
      <button
        class="indicator"
        [class.active]="currentSlide === 5"
        (click)="goToSlide(5)"
      ></button>
    </div>
  </div>
</section>
