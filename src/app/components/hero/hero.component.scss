/* === HERO SECTION - STYLE SEAMLESSHR (ÉPURÉ ET PROFESSIONNEL) === */
.twenty-hero {
  background: var(--white);
  padding: 120px 0 80px;
  position: relative;
  text-align: center;
  overflow: hidden;
}

/* === BADGE ANIMÉ === */
.hero-badge {
  display: inline-block;
  margin-bottom: 32px;

  .badge-text {
    display: inline-block;
    padding: 8px 16px;
    background: var(--gray-100);
    color: var(--gray-700);
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 20px;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }
}

/* === TITRE SEAMLESSHR STYLE === */
.hero-title {
  font-size: 3.5rem;
  font-weight: 700;
  line-height: 1.1;
  color: var(--gray-900);
  margin-bottom: 24px;
  letter-spacing: -0.02em;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 1024px) {
    font-size: 3rem;
  }

  @media (max-width: 768px) {
    font-size: 2.5rem;
  }

  @media (max-width: 480px) {
    font-size: 2rem;
  }
}

/* === SOUS-TITRE SEAMLESSHR STYLE === */
.hero-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  color: var(--gray-600);
  margin-bottom: 40px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 1.125rem;
    margin-bottom: 32px;
    line-height: 1.5;
  }
}

/* === BOUTON D'ACTION SEAMLESSHR STYLE === */
.hero-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 60px;

  @media (max-width: 768px) {
    margin-bottom: 40px;
  }

  .btn-primary {
    display: inline-flex;
    align-items: center;
    padding: 16px 32px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 8px;
    text-decoration: none;
    background: var(--seamless-blue);
    color: var(--white);
    transition: all 0.3s ease;
    border: none;

    &:hover {
      background: var(--seamless-blue-dark);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(26, 115, 232, 0.3);
    }

    @media (max-width: 768px) {
      padding: 14px 28px;
      font-size: 0.9rem;
    }
  }
}

/* === VISUEL AVEC ANIMATIONS === */
.hero-visual {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
}

.dashboard-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);

  .dashboard-image {
    width: 100%;
    height: auto;
    display: block;
  }
}

/* === ÉLÉMENTS FLOTTANTS ANIMÉS === */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;

  .floating-card {
    position: absolute;
    background: var(--white);
    border-radius: 12px;
    padding: 16px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--gray-200);
    animation: float 6s ease-in-out infinite;

    .card-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .card-label {
        font-size: 0.75rem;
        color: var(--gray-500);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .card-value {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--gray-900);
      }
    }

    &.card-1 {
      top: 20%;
      right: -10%;
      animation-delay: 0s;
    }

    &.card-2 {
      bottom: 30%;
      left: -15%;
      animation-delay: 2s;
    }

    &.card-3 {
      top: 60%;
      right: 10%;
      animation-delay: 4s;
    }

    @media (max-width: 768px) {
      display: none;
    }
  }
}

/* === ANIMATIONS === */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* === SECTION CLIENTS - STYLE SEAMLESSHR === */
.seamless-clients {
  background: var(--white);
  padding: 80px 0;
  text-align: center;

  .clients-title {
    font-size: 2rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 60px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.3;

    @media (max-width: 768px) {
      font-size: 1.5rem;
      margin-bottom: 40px;
    }
  }

  .clients-grid {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 60px;
    flex-wrap: wrap;
    max-width: 1000px;
    margin: 0 auto;

    @media (max-width: 768px) {
      gap: 40px;
    }

    .client-item {
      display: flex;
      justify-content: center;
      align-items: center;

      .client-logo {
        height: 50px;
        width: auto;
        max-width: 140px;
        opacity: 0.6;
        transition: all 0.3s ease;
        filter: grayscale(100%);

        &:hover {
          opacity: 1;
          filter: grayscale(0%);
          transform: scale(1.05);
        }

        @media (max-width: 768px) {
          height: 40px;
          max-width: 120px;
        }
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .twenty-hero {
    padding: 120px 0 80px;
  }

  .trusted-companies {
    padding: 60px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
