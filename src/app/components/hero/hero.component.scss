/* === HERO SECTION - PROPRE ET PROFESSIONNEL COMME SEAMLESSHR === */
.hero-section {
  background: var(--white);
  padding: 120px 0 80px;
  position: relative;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }

  @media (max-width: 768px) {
    gap: 40px;
  }
}

/* === PARTIE TEXTUELLE === */
.hero-text {
  .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    color: var(--gray-900);
    margin-bottom: 24px;
    letter-spacing: -0.02em;

    @media (max-width: 1024px) {
      font-size: 3rem;
    }

    @media (max-width: 768px) {
      font-size: 2.5rem;
      margin-bottom: 20px;
    }

    @media (max-width: 480px) {
      font-size: 2rem;
    }
  }

  .hero-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--gray-600);
    margin-bottom: 32px;
    max-width: 500px;

    @media (max-width: 1024px) {
      margin: 0 auto 32px;
    }

    @media (max-width: 768px) {
      font-size: 1.125rem;
      margin-bottom: 28px;
    }
  }

  .hero-cta {
    .btn-primary {
      display: inline-block;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: var(--white);
      padding: 16px 32px;
      font-size: 1.125rem;
      font-weight: 600;
      border-radius: 12px;
      text-decoration: none;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
      }

      @media (max-width: 768px) {
        padding: 14px 28px;
        font-size: 1rem;
      }
    }
  }
}

/* === PARTIE VISUELLE === */
.hero-visual {
  display: flex;
  justify-content: center;
  align-items: center;

  .dashboard-image {
    width: 100%;
    max-width: 600px;
    height: auto;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200);

    @media (max-width: 768px) {
      border-radius: 12px;
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    }
  }
}

/* === SECTION CLIENTS === */
.trusted-companies {
  background: var(--gray-50);
  padding: 60px 0;
  text-align: center;

  .trusted-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 48px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;

    @media (max-width: 768px) {
      font-size: 1.75rem;
      margin-bottom: 36px;
    }
  }

  .companies-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 48px;
    flex-wrap: wrap;
    max-width: 1000px;
    margin: 0 auto;

    @media (max-width: 768px) {
      gap: 32px;
    }

    @media (max-width: 480px) {
      gap: 24px;
    }

    .company-logo {
      height: 48px;
      width: auto;
      opacity: 0.6;
      transition: all 0.3s ease;
      filter: grayscale(100%);

      &:hover {
        opacity: 1;
        filter: grayscale(0%);
        transform: scale(1.05);
      }

      @media (max-width: 768px) {
        height: 40px;
      }

      @media (max-width: 480px) {
        height: 36px;
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 1200px) {
  .hero-section {
    padding: 100px 0 60px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 80px 0 40px;
  }

  .trusted-companies {
    padding: 40px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
