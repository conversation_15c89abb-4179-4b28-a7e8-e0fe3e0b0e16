/* === HERO SECTION - STYLE TWENTY.COM (ÉPURÉ ET MODERNE) === */
.twenty-hero {
  background: var(--white);
  padding: 160px 0 120px;
  position: relative;
  text-align: center;
  overflow: hidden;
}

/* === BADGE ANIMÉ === */
.hero-badge {
  display: inline-block;
  margin-bottom: 32px;

  .badge-text {
    display: inline-block;
    padding: 8px 16px;
    background: var(--gray-100);
    color: var(--gray-700);
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 20px;
    letter-spacing: 0.05em;
    text-transform: uppercase;
  }
}

/* === TITRE ÉNORME COMME TWENTY === */
.hero-title {
  font-size: 6rem;
  font-weight: 900;
  line-height: 0.85;
  color: var(--gray-700);
  margin-bottom: 32px;
  letter-spacing: -0.04em;

  @media (max-width: 1024px) {
    font-size: 5rem;
  }

  @media (max-width: 768px) {
    font-size: 3.5rem;
  }

  @media (max-width: 480px) {
    font-size: 2.8rem;
  }

  .title-highlight {
    color: var(--gray-900);
  }
}

/* === SOUS-TITRE ÉPURÉ === */
.hero-subtitle {
  font-size: 1.5rem;
  line-height: 1.4;
  color: var(--gray-600);
  margin-bottom: 48px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;

  @media (max-width: 768px) {
    font-size: 1.25rem;
    margin-bottom: 40px;
  }
}

/* === BOUTONS D'ACTION AVEC ANIMATIONS === */
.hero-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 80px;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-bottom: 60px;
  }

  .btn-primary,
  .btn-secondary {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 16px 32px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 12px;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }

    &:hover::before {
      left: 100%;
    }

    svg {
      transition: transform 0.3s ease;
    }

    &:hover svg {
      transform: translateX(4px);
    }

    @media (max-width: 768px) {
      padding: 14px 28px;
      font-size: 0.9rem;
    }
  }

  .btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
  }

  .btn-secondary {
    background: var(--white);
    color: var(--gray-700);
    border: 2px solid var(--gray-200);

    &:hover {
      border-color: var(--gray-300);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }
  }
}

/* === VISUEL AVEC ANIMATIONS === */
.hero-visual {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
}

.dashboard-container {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);

  .dashboard-image {
    width: 100%;
    height: auto;
    display: block;
  }
}

/* === ÉLÉMENTS FLOTTANTS ANIMÉS === */
.floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;

  .floating-card {
    position: absolute;
    background: var(--white);
    border-radius: 12px;
    padding: 16px 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border: 1px solid var(--gray-200);
    animation: float 6s ease-in-out infinite;

    .card-content {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .card-label {
        font-size: 0.75rem;
        color: var(--gray-500);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }

      .card-value {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--gray-900);
      }
    }

    &.card-1 {
      top: 20%;
      right: -10%;
      animation-delay: 0s;
    }

    &.card-2 {
      bottom: 30%;
      left: -15%;
      animation-delay: 2s;
    }

    &.card-3 {
      top: 60%;
      right: 10%;
      animation-delay: 4s;
    }

    @media (max-width: 768px) {
      display: none;
    }
  }
}

/* === ANIMATIONS === */
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* === SECTION CLIENTS - STYLE TWENTY.COM === */
.twenty-clients {
  background: var(--white);
  padding: 100px 0;
  text-align: center;

  .clients-intro {
    font-size: 1rem;
    color: var(--gray-500);
    margin-bottom: 60px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;

    @media (max-width: 768px) {
      margin-bottom: 40px;
      font-size: 0.875rem;
    }
  }

  .clients-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 60px;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;

    @media (max-width: 1024px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 40px;
    }

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 30px;
    }

    .client-item {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-4px);
        background: var(--gray-50);
      }

      .client-logo {
        height: 40px;
        width: auto;
        max-width: 120px;
        opacity: 0.4;
        transition: all 0.3s ease;
        filter: grayscale(100%);

        &:hover {
          opacity: 0.8;
          filter: grayscale(0%);
        }

        @media (max-width: 768px) {
          height: 32px;
        }
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .twenty-hero {
    padding: 120px 0 80px;
  }

  .trusted-companies {
    padding: 60px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
