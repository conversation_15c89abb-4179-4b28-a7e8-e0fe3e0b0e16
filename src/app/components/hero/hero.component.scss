// Hero Section - Fusion SeamlessHR x Edomatch
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  padding-top: 80px; // Espace pour la navbar fixe
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;

  .hero-gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-gradient);
    opacity: 0.1;
  }

  .hero-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: radial-gradient(
        circle at 25% 25%,
        var(--primary-blue-light) 0%,
        transparent 50%
      ),
      radial-gradient(
        circle at 75% 75%,
        var(--accent-purple-light) 0%,
        transparent 50%
      );
    opacity: 0.05;
  }
}

.hero-content {
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4xl);
  align-items: center;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: var(--spacing-3xl);
    text-align: center;
  }
}

// Texte du hero
.hero-text {
  .hero-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    background: var(--bg-gradient);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--radius-2xl);
    font-size: var(--text-sm);
    font-weight: 600;
    margin-bottom: var(--spacing-xl);
    box-shadow: var(--shadow-lg);

    .badge-icon {
      font-size: var(--text-base);
    }
  }

  .hero-title {
    font-size: var(--text-6xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--spacing-xl);
    color: var(--gray-900);

    @media (max-width: 768px) {
      font-size: var(--text-4xl);
    }
  }

  .hero-description {
    font-size: var(--text-xl);
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-2xl);
    max-width: 600px;

    @media (max-width: 1024px) {
      margin: 0 auto var(--spacing-2xl);
    }

    @media (max-width: 768px) {
      font-size: var(--text-lg);
    }
  }
}

// Actions du hero
.hero-actions {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-3xl);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    svg {
      transition: transform var(--transition-fast);
    }

    &:hover svg {
      transform: translateX(4px);
    }
  }
}

// Statistiques du hero
.hero-stats {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);

  @media (max-width: 1024px) {
    justify-content: center;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: var(--spacing-lg);
  }

  .stat-item {
    text-align: center;

    .stat-number {
      font-size: var(--text-3xl);
      font-weight: 800;
      color: var(--primary-blue);
      margin-bottom: var(--spacing-xs);
      font-family: var(--font-secondary);
    }

    .stat-label {
      font-size: var(--text-sm);
      color: var(--gray-600);
      font-weight: 500;
    }
  }

  .stat-divider {
    width: 1px;
    height: 40px;
    background: var(--gray-300);

    @media (max-width: 768px) {
      display: none;
    }
  }
}

// Partie visuelle du hero
.hero-visual {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;

  .hero-image-container {
    position: relative;
    max-width: 600px;
    width: 100%;

    .hero-image {
      width: 100%;
      height: auto;
      border-radius: var(--radius-2xl);
      box-shadow: var(--shadow-xl);
      border: 1px solid var(--gray-200);
    }
  }
}

// Cartes flottantes
.floating-card {
  position: absolute;
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  min-width: 200px;
  backdrop-filter: blur(10px);
  animation: float 6s ease-in-out infinite;

  @media (max-width: 768px) {
    display: none; // Masquer sur mobile pour éviter l'encombrement
  }

  .card-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    background: var(--bg-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-lg);
    flex-shrink: 0;
  }

  .card-content {
    flex: 1;

    .card-title {
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--gray-900);
      margin-bottom: var(--spacing-xs);
    }

    .card-desc {
      font-size: var(--text-xs);
      color: var(--gray-600);
    }
  }

  .card-metric {
    font-size: var(--text-xl);
    font-weight: 800;
    color: var(--primary-blue);
    font-family: var(--font-secondary);
  }

  &.card-1 {
    top: 10%;
    right: -10%;
    animation-delay: 0s;
  }

  &.card-2 {
    bottom: 30%;
    left: -15%;
    animation-delay: 2s;
  }

  &.card-3 {
    bottom: 10%;
    right: 10%;
    animation-delay: 4s;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// Section clients
.hero-clients {
  margin-top: var(--spacing-4xl);
  text-align: center;

  .clients-title {
    font-size: var(--text-base);
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
    font-weight: 500;
  }

  .clients-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-xl);
    flex-wrap: wrap;

    @media (max-width: 768px) {
      gap: var(--spacing-lg);
    }

    .client-logo {
      height: 40px;
      width: auto;
      opacity: 0.6;
      transition: opacity var(--transition-fast);
      filter: grayscale(100%);

      &:hover {
        opacity: 1;
        filter: grayscale(0%);
      }

      @media (max-width: 768px) {
        height: 32px;
      }
    }
  }
}

// Ajustements pour le contenu principal
:host {
  display: block;
}
