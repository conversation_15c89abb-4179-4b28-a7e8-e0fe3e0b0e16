/* === SECTION CTA FINALE - STYLE TWENTY.COM (ÉPURÉ ET MODERNE) === */
.twenty-final-cta {
  padding: 120px 0;
  background: var(--gray-50);
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;

  .cta-title {
    font-size: 5rem;
    font-weight: 900;
    color: var(--gray-700);
    margin-bottom: 32px;
    line-height: 0.85;
    letter-spacing: -0.04em;

    @media (max-width: 1024px) {
      font-size: 4rem;
    }

    @media (max-width: 768px) {
      font-size: 3rem;
    }

    .title-highlight {
      color: var(--gray-900);
    }
  }

  .cta-description {
    font-size: 1.25rem;
    line-height: 1.8;
    color: var(--gray-600);
    margin-bottom: 48px;

    @media (max-width: 768px) {
      font-size: 1.125rem;
      line-height: 1.7;
      margin-bottom: 40px;
    }
  }

  .cta-actions {
    display: flex;
    justify-content: center;
    gap: 20px;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
      gap: 16px;
    }

    .btn-primary {
      display: inline-block;
      background: var(--white);
      color: #667eea;
      padding: 16px 32px;
      font-size: 1.125rem;
      font-weight: 600;
      border-radius: 12px;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
      }

      @media (max-width: 768px) {
        padding: 14px 28px;
        font-size: 1rem;
      }
    }

    .btn-secondary {
      display: inline-block;
      background: transparent;
      color: var(--white);
      border: 2px solid var(--white);
      padding: 14px 30px;
      font-size: 1.125rem;
      font-weight: 600;
      border-radius: 12px;
      text-decoration: none;
      transition: all 0.3s ease;

      &:hover {
        background: var(--white);
        color: #667eea;
        transform: translateY(-2px);
      }

      @media (max-width: 768px) {
        padding: 12px 26px;
        font-size: 1rem;
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .final-cta-section {
    padding: 80px 0;
  }
}

@media (max-width: 480px) {
  .final-cta-section {
    padding: 60px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
