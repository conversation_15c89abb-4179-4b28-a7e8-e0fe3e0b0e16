/* === SECTION SÉCURITÉ - STYLE TWENTY.COM (ÉPURÉ ET MODERNE) === */
.twenty-security {
  padding: 120px 0;
  background: var(--gray-50);
}

/* === EXPANSION GLOBALE === */
.global-expansion {
  margin-bottom: 120px;

  .expansion-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;

    @media (max-width: 1024px) {
      grid-template-columns: 1fr;
      gap: 60px;
      text-align: center;
    }
  }

  .expansion-text {
    .section-title {
      font-size: 5rem;
      font-weight: 900;
      color: var(--gray-700);
      margin-bottom: 32px;
      line-height: 0.85;
      letter-spacing: -0.04em;

      @media (max-width: 1024px) {
        font-size: 4rem;
      }

      @media (max-width: 768px) {
        font-size: 3rem;
      }

      .title-highlight {
        color: var(--gray-900);
      }
    }

    .section-description {
      font-size: 1.125rem;
      line-height: 1.6;
      color: var(--gray-600);
      max-width: 500px;

      @media (max-width: 1024px) {
        margin: 0 auto;
      }
    }
  }

  .expansion-visual {
    display: flex;
    justify-content: center;
    align-items: center;

    .world-map {
      .map-image {
        width: 100%;
        max-width: 500px;
        height: auto;
      }
    }
  }
}

/* === SÉCURITÉ FORTRESS === */
.fortress-security {
  .security-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
  }

  .security-text {
    margin-bottom: 60px;

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--gray-900);
      margin-bottom: 24px;
      line-height: 1.2;

      @media (max-width: 768px) {
        font-size: 2rem;
      }
    }

    .section-description {
      font-size: 1.125rem;
      line-height: 1.6;
      color: var(--gray-600);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .certifications {
    display: flex;
    justify-content: center;
    gap: 60px;

    @media (max-width: 768px) {
      gap: 40px;
    }

    @media (max-width: 480px) {
      flex-direction: column;
      gap: 30px;
    }

    .cert-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .cert-badge {
        width: 80px;
        height: 80px;
        background: var(--gray-50);
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);

        .cert-logo {
          height: 50px;
          width: auto;
        }
      }

      .cert-text {
        text-align: center;

        h3 {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--gray-900);
          margin-bottom: 4px;
        }

        p {
          font-size: 1rem;
          color: var(--gray-600);
          margin: 0;
        }
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .security-section {
    padding: 60px 0;
  }

  .global-expansion {
    margin-bottom: 80px;
  }

  .fortress-security .security-text {
    margin-bottom: 40px;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
