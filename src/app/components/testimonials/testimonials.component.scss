/* === SECTION TÉMOIGNAGES - STYLE TWENTY.COM (ÉPURÉ ET MODERNE) === */
.twenty-testimonials {
  padding: 120px 0;
  background: var(--white);
  text-align: center;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }
}

.section-title {
  font-size: 5rem;
  font-weight: 900;
  color: var(--gray-700);
  margin-bottom: 80px;
  line-height: 0.85;
  letter-spacing: -0.04em;

  @media (max-width: 1024px) {
    font-size: 4rem;
  }

  @media (max-width: 768px) {
    font-size: 3rem;
    margin-bottom: 60px;
  }

  .title-highlight {
    color: var(--gray-900);
  }
}

.testimonials-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 40px;
  max-width: 1000px;
  margin: 0 auto;

  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

.testimonial-card {
  background: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: 16px;
  padding: 32px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-color: var(--gray-300);
  }

  .testimonial-photo {
    margin-bottom: 24px;

    img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .testimonial-content {
    p {
      font-size: 1.125rem;
      line-height: 1.8;
      color: var(--gray-700);
      margin-bottom: 20px;
      font-style: italic;

      @media (max-width: 768px) {
        font-size: 1rem;
        line-height: 1.7;
      }
    }

    .testimonial-name {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--gray-500);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .twenty-testimonials {
    padding: 80px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}
