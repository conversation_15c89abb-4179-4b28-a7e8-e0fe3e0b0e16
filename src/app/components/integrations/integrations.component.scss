/* === SECTION INTÉGRATIONS - STYLE TWENTY.COM (ÉPURÉ ET MODERNE) === */
.twenty-integrations {
  padding: 120px 0;
  background: var(--white);
}

.integrations-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 100px;
  align-items: center;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }
}

/* === PARTIE TEXTE AVEC TITRE ÉNORME === */
.integrations-text {
  .section-title {
    font-size: 5rem;
    font-weight: 900;
    line-height: 0.85;
    color: var(--gray-700);
    margin-bottom: 32px;
    letter-spacing: -0.04em;

    @media (max-width: 1024px) {
      font-size: 4rem;
    }

    @media (max-width: 768px) {
      font-size: 3rem;
    }

    .title-highlight {
      color: var(--gray-900);
    }
  }

  .section-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--gray-600);

    @media (max-width: 768px) {
      font-size: 1.125rem;
    }
  }
}

/* === LOGOS DES INTÉGRATIONS === */
.integrations-logos {
  .logos-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;

    @media (max-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 30px;
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }

    .logo-item {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 24px;
      background: var(--white);
      border: 1px solid var(--gray-200);
      border-radius: 16px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        border-color: var(--gray-300);
      }

      .integration-logo {
        height: 40px;
        width: auto;
        max-width: 80px;
        opacity: 0.7;
        transition: all 0.3s ease;
        filter: grayscale(100%);

        &:hover {
          opacity: 1;
          filter: grayscale(0%);
        }

        @media (max-width: 768px) {
          height: 32px;
        }
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .twenty-integrations {
    padding: 80px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
