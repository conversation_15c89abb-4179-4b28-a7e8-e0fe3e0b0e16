/* === SECTION INTÉGRATIONS - STYLE SEAMLESSHR === */
.integrations-section {
  padding: 80px 0;
  background: var(--gray-50);
}

.integrations-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;

  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }
}

/* === PARTIE TEXTUELLE === */
.integrations-text {
  .section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #667eea;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .section-subtitle {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 20px;
    line-height: 1.2;

    @media (max-width: 768px) {
      font-size: 2rem;
    }
  }

  .section-description {
    font-size: 1.125rem;
    line-height: 1.6;
    color: var(--gray-600);
    max-width: 500px;

    @media (max-width: 1024px) {
      margin: 0 auto;
    }
  }
}

/* === LOGOS DES INTÉGRATIONS === */
.integrations-logos {
  .logos-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 32px;
    align-items: center;

    @media (max-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;
    }

    @media (max-width: 480px) {
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
    }

    .logo-item {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      background: var(--white);
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      }

      .integration-logo {
        height: 40px;
        width: auto;
        max-width: 100%;
        opacity: 0.7;
        transition: all 0.3s ease;

        &:hover {
          opacity: 1;
        }

        @media (max-width: 768px) {
          height: 32px;
        }
      }
    }
  }
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .integrations-section {
    padding: 60px 0;
  }
}

/* === AJUSTEMENTS GLOBAUX === */
:host {
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;

  @media (max-width: 768px) {
    padding: 0 20px;
  }

  @media (max-width: 480px) {
    padding: 0 16px;
  }
}
