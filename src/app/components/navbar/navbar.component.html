<header class="seamless-navbar">
  <nav class="navbar container">
    <!-- Logo (Style SeamlessHR propre) -->
    <div class="navbar-brand">
      <img src="assets/images/logo.png" alt="CompulseHR" class="brand-logo" />
    </div>

    <!-- Navigation principale (Style SeamlessHR) -->
    <div class="navbar-nav" [class.active]="isMenuOpen">
      <div class="nav-group">
        <!-- Méga-menu Produit (Exactement comme SeamlessHR) -->
        <div
          class="nav-item has-mega-menu"
          (mouseenter)="showDropdown('produit')"
          (mouseleave)="hideDropdown()"
        >
          <a href="#" class="nav-link">
            Produit
            <svg
              class="dropdown-icon"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M6 9l6 6 6-6"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </a>

          <!-- Méga-menu (Style SeamlessHR) -->
          <div class="mega-menu" *ngIf="activeDropdown === 'produit'">
            <div class="mega-menu-container">
              <div class="mega-menu-content">
                <!-- Section SIRH -->
                <div class="menu-column">
                  <h4 class="menu-column-title">
                    <span class="menu-icon">🏢</span>
                    SIRH
                  </h4>
                  <ul class="menu-items">
                    <li>
                      <a href="#" class="menu-item">Gestion des employés</a>
                    </li>
                    <li><a href="#" class="menu-item">Organigramme</a></li>
                    <li><a href="#" class="menu-item">Documents RH</a></li>
                    <li><a href="#" class="menu-item">Onboarding</a></li>
                    <li><a href="#" class="menu-item">Offboarding</a></li>
                  </ul>
                </div>

                <!-- Section Paie -->
                <div class="menu-column">
                  <h4 class="menu-column-title">
                    <span class="menu-icon">💰</span>
                    Paie
                  </h4>
                  <ul class="menu-items">
                    <li>
                      <a href="#" class="menu-item">Calcul automatique</a>
                    </li>
                    <li><a href="#" class="menu-item">Bulletins de paie</a></li>
                    <li>
                      <a href="#" class="menu-item">Déclarations sociales</a>
                    </li>
                    <li><a href="#" class="menu-item">Conformité légale</a></li>
                    <li><a href="#" class="menu-item">Reporting paie</a></li>
                  </ul>
                </div>

                <!-- Section Performance -->
                <div class="menu-column">
                  <h4 class="menu-column-title">
                    <span class="menu-icon">📊</span>
                    Performance
                  </h4>
                  <ul class="menu-items">
                    <li><a href="#" class="menu-item">Évaluations 360°</a></li>
                    <li><a href="#" class="menu-item">Objectifs & KPI</a></li>
                    <li>
                      <a href="#" class="menu-item">Plans de développement</a>
                    </li>
                    <li><a href="#" class="menu-item">Feedback continu</a></li>
                    <li><a href="#" class="menu-item">Analytics RH</a></li>
                  </ul>
                </div>

                <!-- Section Recrutement -->
                <div class="menu-column">
                  <h4 class="menu-column-title">
                    <span class="menu-icon">🎯</span>
                    Recrutement
                  </h4>
                  <ul class="menu-items">
                    <li><a href="#" class="menu-item">ATS intelligent</a></li>
                    <li><a href="#" class="menu-item">Matching IA</a></li>
                    <li><a href="#" class="menu-item">Entretiens vidéo</a></li>
                    <li><a href="#" class="menu-item">Tests techniques</a></li>
                    <li><a href="#" class="menu-item">Marque employeur</a></li>
                  </ul>
                </div>

                <!-- Section Temps & Présence -->
                <div class="menu-column">
                  <h4 class="menu-column-title">
                    <span class="menu-icon">⏰</span>
                    Temps & Présence
                  </h4>
                  <ul class="menu-items">
                    <li><a href="#" class="menu-item">Pointage digital</a></li>
                    <li>
                      <a href="#" class="menu-item">Gestion des congés</a>
                    </li>
                    <li><a href="#" class="menu-item">Planning équipes</a></li>
                    <li>
                      <a href="#" class="menu-item">Heures supplémentaires</a>
                    </li>
                    <li><a href="#" class="menu-item">Télétravail</a></li>
                  </ul>
                </div>

                <!-- Section Avantages -->
                <div class="menu-column">
                  <h4 class="menu-column-title">
                    <span class="menu-icon">🏆</span>
                    Avantages salariés
                  </h4>
                  <ul class="menu-items">
                    <li><a href="#" class="menu-item">Titres restaurant</a></li>
                    <li>
                      <a href="#" class="menu-item">Mutuelle entreprise</a>
                    </li>
                    <li><a href="#" class="menu-item">Épargne salariale</a></li>
                    <li><a href="#" class="menu-item">Mobilité durable</a></li>
                    <li>
                      <a href="#" class="menu-item">Bien-être au travail</a>
                    </li>
                  </ul>
                </div>
              </div>

              <!-- Section mise en avant (Style Edomatch) -->
              <div class="mega-menu-highlight">
                <div class="highlight-card">
                  <div class="highlight-icon">✨</div>
                  <h5>Nouveau : IA Générative</h5>
                  <p>
                    Automatisez vos processus RH avec notre assistant IA
                    révolutionnaire
                  </p>
                  <a href="#" class="btn btn-sm btn-primary">Découvrir l'IA</a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Menu À propos -->
        <div
          class="nav-item has-dropdown"
          (mouseenter)="showDropdown('apropos')"
          (mouseleave)="hideDropdown()"
        >
          <a href="#" class="nav-link">
            À propos
            <svg
              class="dropdown-icon"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M6 9l6 6 6-6"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </a>
          <div class="dropdown-menu" *ngIf="activeDropdown === 'apropos'">
            <a href="#" class="dropdown-item">Notre histoire</a>
            <a href="#" class="dropdown-item">Équipe</a>
            <a href="#" class="dropdown-item">Carrières</a>
            <a href="#" class="dropdown-item">Presse</a>
            <a href="#" class="dropdown-item">CompulseForGood</a>
          </div>
        </div>

        <!-- Menu Partenariat -->
        <div
          class="nav-item has-dropdown"
          (mouseenter)="showDropdown('partenariat')"
          (mouseleave)="hideDropdown()"
        >
          <a href="#" class="nav-link">
            Partenariat
            <svg
              class="dropdown-icon"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M6 9l6 6 6-6"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </a>
          <div class="dropdown-menu" *ngIf="activeDropdown === 'partenariat'">
            <a href="#" class="dropdown-item">Devenir partenaire</a>
            <a href="#" class="dropdown-item">Programme de parrainage</a>
            <a href="#" class="dropdown-item">Intégrateurs</a>
            <a href="#" class="dropdown-item">Communauté</a>
          </div>
        </div>

        <!-- Menu Insights -->
        <div
          class="nav-item has-dropdown"
          (mouseenter)="showDropdown('insights')"
          (mouseleave)="hideDropdown()"
        >
          <a href="#" class="nav-link">
            Insights
            <svg
              class="dropdown-icon"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
            >
              <path
                d="M6 9l6 6 6-6"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </a>
          <div class="dropdown-menu" *ngIf="activeDropdown === 'insights'">
            <a href="#" class="dropdown-item">Blog RH</a>
            <a href="#" class="dropdown-item">Webinaires</a>
            <a href="#" class="dropdown-item">Livres blancs</a>
            <a href="#" class="dropdown-item">Études de cas</a>
            <a href="#" class="dropdown-item">Glossaire RH</a>
          </div>
        </div>

        <!-- Liens simples -->
        <a href="#tarifs" class="nav-link">Tarifs</a>
        <a href="#contact" class="nav-link">Contact</a>
      </div>

      <!-- Actions CTA (Style Edomatch) -->
      <div class="navbar-actions">
        <a href="#connexion" class="btn btn-ghost btn-sm">Se connecter</a>
        <a href="#demo" class="btn btn-primary btn-sm">
          <span>Demander une démo</span>
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path
              d="M5 12h14M12 5l7 7-7 7"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            />
          </svg>
        </a>
      </div>
    </div>

    <!-- Menu mobile toggle -->
    <button
      class="mobile-menu-toggle"
      (click)="toggleMobileMenu()"
      [class.active]="isMenuOpen"
    >
      <span></span>
      <span></span>
      <span></span>
    </button>
  </nav>
</header>
