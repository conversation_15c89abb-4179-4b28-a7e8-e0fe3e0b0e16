// Palette inspirée de seamlesshr.com et edomatch.com
$primary: #1a73e8;
$secondary: #fbbc05;
$accent: #34a853;
$dark: #222;
$light: #f5f7fa;
$grey: #e0e0e0;

body {
  font-family: "Inter", "Segoe UI", <PERSON><PERSON>, sans-serif;
  background: $light;
  color: $dark;
  margin: 0;
  padding: 0;
}

// HEADER
.main-header {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  position: sticky;
  top: 0;
  z-index: 100;
}
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.2rem 2rem;
}
.logo-area {
  display: flex;
  align-items: center;
  gap: 0.7rem;
}
.logo-img {
  height: 2.2rem;
  width: auto;
  border-radius: 6px;
  background: #fff;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.08);
}
.logo-text {
  font-weight: 700;
  font-size: 1.5rem;
  color: $primary;
  letter-spacing: 1px;
}
.nav-links {
  display: flex;
  gap: 2.2rem;
  list-style: none;
  margin: 0;
  padding: 0;
}
.nav-links a {
  text-decoration: none;
  color: $dark;
  font-weight: 500;
  font-size: 1.08rem;
  transition: color 0.2s;
  &:hover {
    color: $primary;
  }
}
.cta-btn {
  background: $primary;
  color: #fff;
  padding: 0.7rem 1.5rem;
  border-radius: 30px;
  font-weight: 600;
  text-decoration: none;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.08);
  &:hover {
    background: darken($primary, 7%);
    box-shadow: 0 4px 16px rgba(26, 115, 232, 0.12);
  }
}

// HERO
.hero-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 2.5rem auto 0 auto;
  padding: 2.5rem 2rem 2rem 2rem;
  background: linear-gradient(90deg, $primary 0%, $secondary 100%);
  border-radius: 32px;
  color: #fff;
  min-height: 420px;
  box-shadow: 0 8px 32px rgba(26, 115, 232, 0.08);
  position: relative;
  overflow: hidden;
}
.hero-content {
  max-width: 520px;
  z-index: 2;
}
.hero-content h1 {
  font-size: 2.7rem;
  font-weight: 800;
  margin-bottom: 1.2rem;
  line-height: 1.1;
}
.hero-content p {
  font-size: 1.18rem;
  margin-bottom: 2.2rem;
  color: #f5f7fa;
}
.cta-hero {
  font-size: 1.1rem;
  padding: 0.9rem 2.2rem;
  margin-bottom: 2.2rem;
  display: inline-block;
}
.hero-clients {
  margin-top: 2.2rem;
  display: flex;
  align-items: center;
  gap: 1.2rem;
  font-size: 1rem;
  color: #fff;
  opacity: 0.92;
}
.client-logo {
  height: 2.1rem;
  width: auto;
  background: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.08);
  padding: 0.2rem 0.5rem;
}
.hero-visual {
  flex: 1 1 340px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  z-index: 1;
}
.hero-visual img {
  width: 340px;
  max-width: 100%;
  border-radius: 18px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
}

// Responsive
@media (max-width: 1100px) {
  .navbar,
  .hero-section {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  .hero-section {
    flex-direction: column;
    text-align: center;
    gap: 2.5rem;
    min-height: 0;
  }
  .hero-visual {
    justify-content: center;
    margin-top: 2rem;
  }
  .hero-visual img {
    width: 220px;
  }
  .hero-content {
    max-width: 100%;
  }
  .hero-clients {
    justify-content: center;
  }
}
@media (max-width: 700px) {
  .navbar {
    flex-direction: column;
    gap: 1.2rem;
    padding: 1rem 0.5rem;
  }
  .nav-links {
    gap: 1.2rem;
  }
  .hero-section {
    padding: 1.2rem 0.5rem;
    border-radius: 18px;
  }
  .hero-visual img {
    width: 140px;
  }
  .client-logo {
    height: 1.2rem;
  }
}

// Base pour les prochaines sections (à compléter)
.section-title {
  font-size: 2rem;
  color: $primary;
  margin-bottom: 1.2rem;
  font-weight: 700;
}
