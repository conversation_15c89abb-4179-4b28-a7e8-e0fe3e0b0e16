/* SYSTÈME DE DESIGN COMPLET - STYLE TWENTY.COM (ÉPURÉ ET MODERNE) */

// Import AOS pour les animations
@import "~aos/dist/aos.css";

// Import Google Fonts - Exactement comme les deux sites
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap");

// Variables CSS personnalisées - Fusion parfaite des deux palettes
:root {
  // === COULEURS PRIMAIRES (SeamlessHR + Edomatch) ===
  --seamless-blue: #1a73e8; // Bleu principal SeamlessHR
  --seamless-blue-dark: #1557b0; // Version foncée
  --seamless-blue-light: #4285f4; // Version claire

  --edomatch-purple: #6366f1; // Violet principal Edomatch
  --edomatch-purple-dark: #4f46e5; // Version foncée
  --edomatch-purple-light: #8b5cf6; // Version claire

  // === COULEURS SECONDAIRES ===
  --seamless-orange: #fbbc05; // Orange SeamlessHR
  --seamless-green: #34a853; // Vert SeamlessHR
  --edomatch-cyan: #06b6d4; // Cyan Edomatch
  --edomatch-pink: #ec4899; // Rose Edomatch

  // === COULEURS NEUTRES (Harmonisées) ===
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --black: #000000;

  // === COULEURS DE FOND ===
  --bg-primary: var(--white);
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-dark: #0f172a;

  // === GRADIENTS SIGNATURE (Fusion des deux styles) ===
  --gradient-seamless: linear-gradient(
    135deg,
    var(--seamless-blue) 0%,
    var(--seamless-orange) 100%
  );
  --gradient-edomatch: linear-gradient(
    135deg,
    var(--edomatch-purple) 0%,
    var(--edomatch-pink) 100%
  );
  --gradient-fusion: linear-gradient(
    135deg,
    var(--seamless-blue) 0%,
    var(--edomatch-purple) 50%,
    var(--seamless-orange) 100%
  );
  --gradient-hero: linear-gradient(
    135deg,
    var(--seamless-blue-light) 0%,
    var(--edomatch-purple-light) 100%
  );
  --gradient-cta: linear-gradient(
    135deg,
    var(--seamless-blue) 0%,
    var(--edomatch-purple-dark) 100%
  );

  // === COULEURS SÉMANTIQUES ===
  --success: var(--seamless-green);
  --warning: var(--seamless-orange);
  --error: #ef4444;
  --info: var(--seamless-blue);

  // === TYPOGRAPHIE (Exactement comme les deux sites) ===
  --font-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-secondary: "Poppins", sans-serif;
  --font-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;

  // === ÉCHELLE TYPOGRAPHIQUE (Optimisée pour les deux styles) ===
  --text-xs: 0.75rem; // 12px
  --text-sm: 0.875rem; // 14px
  --text-base: 1rem; // 16px
  --text-lg: 1.125rem; // 18px
  --text-xl: 1.25rem; // 20px
  --text-2xl: 1.5rem; // 24px
  --text-3xl: 1.875rem; // 30px
  --text-4xl: 2.25rem; // 36px
  --text-5xl: 3rem; // 48px
  --text-6xl: 3.75rem; // 60px
  --text-7xl: 4.5rem; // 72px
  --text-8xl: 6rem; // 96px

  // === POIDS DE POLICE ===
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
  --font-black: 900;

  // === SYSTÈME D'ESPACEMENT (Cohérent avec les deux sites) ===
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0-5: 0.125rem; // 2px
  --spacing-1: 0.25rem; // 4px
  --spacing-1-5: 0.375rem; // 6px
  --spacing-2: 0.5rem; // 8px
  --spacing-2-5: 0.625rem; // 10px
  --spacing-3: 0.75rem; // 12px
  --spacing-3-5: 0.875rem; // 14px
  --spacing-4: 1rem; // 16px
  --spacing-5: 1.25rem; // 20px
  --spacing-6: 1.5rem; // 24px
  --spacing-7: 1.75rem; // 28px
  --spacing-8: 2rem; // 32px
  --spacing-9: 2.25rem; // 36px
  --spacing-10: 2.5rem; // 40px
  --spacing-12: 3rem; // 48px
  --spacing-14: 3.5rem; // 56px
  --spacing-16: 4rem; // 64px
  --spacing-20: 5rem; // 80px
  --spacing-24: 6rem; // 96px
  --spacing-28: 7rem; // 112px
  --spacing-32: 8rem; // 128px

  // === RAYONS DE BORDURE (Style moderne) ===
  --radius-none: 0;
  --radius-sm: 0.125rem; // 2px
  --radius-base: 0.25rem; // 4px
  --radius-md: 0.375rem; // 6px
  --radius-lg: 0.5rem; // 8px
  --radius-xl: 0.75rem; // 12px
  --radius-2xl: 1rem; // 16px
  --radius-3xl: 1.5rem; // 24px
  --radius-full: 9999px;

  // === OMBRES (Inspirées des deux sites) ===
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);
  --shadow-none: 0 0 #0000;

  // === TRANSITIONS ET ANIMATIONS ===
  --transition-all: all 0.15s ease-in-out;
  --transition-colors: color 0.15s ease-in-out,
    background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
  --transition-opacity: opacity 0.15s ease-in-out;
  --transition-shadow: box-shadow 0.15s ease-in-out;
  --transition-transform: transform 0.15s ease-in-out;

  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  // === COURBES D'ANIMATION ===
  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* === RESET ET STYLES DE BASE === */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  scroll-padding-top: 80px; // Pour la navbar fixe
  -webkit-text-size-adjust: 100%;
  -moz-text-size-adjust: 100%;
  text-size-adjust: 100%;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
  min-height: 100vh;
}

/* === TYPOGRAPHIE GLOBALE === */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-secondary);
  font-weight: var(--font-bold);
  line-height: 1.2;
  color: var(--gray-900);
  margin-bottom: var(--spacing-4);
  letter-spacing: -0.025em;
}

h1 {
  font-size: var(--text-6xl);
  font-weight: var(--font-extrabold);
  line-height: 1.1;

  @media (max-width: 768px) {
    font-size: var(--text-4xl);
  }
}

h2 {
  font-size: var(--text-5xl);
  font-weight: var(--font-bold);

  @media (max-width: 768px) {
    font-size: var(--text-3xl);
  }
}

h3 {
  font-size: var(--text-4xl);

  @media (max-width: 768px) {
    font-size: var(--text-2xl);
  }
}

h4 {
  font-size: var(--text-3xl);

  @media (max-width: 768px) {
    font-size: var(--text-xl);
  }
}

h5 {
  font-size: var(--text-2xl);
}

h6 {
  font-size: var(--text-xl);
}

p {
  margin-bottom: var(--spacing-4);
  color: var(--gray-600);
  line-height: 1.7;
}

a {
  color: var(--seamless-blue);
  text-decoration: none;
  transition: var(--transition-colors);

  &:hover {
    color: var(--seamless-blue-dark);
  }

  &:focus {
    outline: 2px solid var(--seamless-blue);
    outline-offset: 2px;
  }
}

strong,
b {
  font-weight: var(--font-semibold);
}

em,
i {
  font-style: italic;
}

small {
  font-size: var(--text-sm);
}

code {
  font-family: var(--font-mono);
  font-size: 0.875em;
  background-color: var(--gray-100);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-base);
  color: var(--gray-800);
}

/* === CLASSES UTILITAIRES === */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-6);

  @media (max-width: 768px) {
    padding: 0 var(--spacing-4);
  }
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-6);

  @media (max-width: 768px) {
    padding: 0 var(--spacing-4);
  }
}

.section {
  padding: var(--spacing-24) 0;

  @media (max-width: 768px) {
    padding: var(--spacing-16) 0;
  }
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-16);

  .section-title {
    font-size: var(--text-5xl);
    font-weight: var(--font-extrabold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);

    @media (max-width: 768px) {
      font-size: var(--text-3xl);
    }
  }

  .section-subtitle {
    font-size: var(--text-xl);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;

    @media (max-width: 768px) {
      font-size: var(--text-lg);
    }
  }
}

/* === SYSTÈME DE BOUTONS (Fusion SeamlessHR x Edomatch) === */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  font-family: var(--font-primary);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  line-height: 1.5;
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-all);
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }

  &:focus {
    outline: 2px solid var(--seamless-blue);
    outline-offset: 2px;
  }
}

// Bouton primaire (Style Edomatch avec couleurs SeamlessHR)
.btn-primary {
  background: var(--gradient-cta);
  color: var(--white);
  border-color: transparent;
  box-shadow: var(--shadow-md);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--gradient-fusion);
  }

  &:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
  }
}

// Bouton secondaire (Style SeamlessHR)
.btn-secondary {
  background: var(--white);
  color: var(--seamless-blue);
  border-color: var(--seamless-blue);

  &:hover:not(:disabled) {
    background: var(--seamless-blue);
    color: var(--white);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

// Bouton outline (Style moderne)
.btn-outline {
  background: transparent;
  color: var(--gray-700);
  border-color: var(--gray-300);

  &:hover:not(:disabled) {
    background: var(--gray-50);
    border-color: var(--seamless-blue);
    color: var(--seamless-blue);
  }
}

// Bouton ghost (Edomatch style)
.btn-ghost {
  background: transparent;
  color: var(--gray-600);
  border-color: transparent;

  &:hover:not(:disabled) {
    background: var(--gray-100);
    color: var(--gray-900);
  }
}

// Tailles de boutons
.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--text-sm);
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--text-lg);
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--spacing-5) var(--spacing-10);
  font-size: var(--text-xl);
  border-radius: var(--radius-2xl);
}

// Bouton pleine largeur
.btn-full {
  width: 100%;
}

// Bouton avec icône
.btn-icon {
  padding: var(--spacing-3);

  &.btn-sm {
    padding: var(--spacing-2);
  }

  &.btn-lg {
    padding: var(--spacing-4);
  }
}

/* === SYSTÈME DE GRILLE === */
.grid {
  display: grid;
  gap: var(--spacing-6);
}

.grid-cols-1 {
  grid-template-columns: repeat(1, 1fr);
}
.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}
.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}
.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}
.grid-cols-5 {
  grid-template-columns: repeat(5, 1fr);
}
.grid-cols-6 {
  grid-template-columns: repeat(6, 1fr);
}

@media (max-width: 1024px) {
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  .grid-cols-5 {
    grid-template-columns: repeat(3, 1fr);
  }
  .grid-cols-6 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
  .grid-cols-3 {
    grid-template-columns: 1fr;
  }
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
  .grid-cols-5 {
    grid-template-columns: 1fr;
  }
  .grid-cols-6 {
    grid-template-columns: 1fr;
  }
}

// Gaps personnalisés
.gap-1 {
  gap: var(--spacing-1);
}
.gap-2 {
  gap: var(--spacing-2);
}
.gap-3 {
  gap: var(--spacing-3);
}
.gap-4 {
  gap: var(--spacing-4);
}
.gap-6 {
  gap: var(--spacing-6);
}
.gap-8 {
  gap: var(--spacing-8);
}
.gap-12 {
  gap: var(--spacing-12);
}

/* === CARTES (Style moderne) === */
.card {
  background: var(--bg-primary);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  transition: var(--transition-all);

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--gray-300);
  }
}

.card-header {
  margin-bottom: var(--spacing-4);

  .card-title {
    font-size: var(--text-xl);
    font-weight: var(--font-semibold);
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
  }

  .card-subtitle {
    color: var(--gray-600);
    font-size: var(--text-sm);
  }
}

.card-body {
  margin-bottom: var(--spacing-4);

  p {
    color: var(--gray-600);
    line-height: 1.6;
  }
}

.card-footer {
  border-top: 1px solid var(--gray-200);
  padding-top: var(--spacing-4);
  margin-top: var(--spacing-4);
}

/* === CLASSES UTILITAIRES TEXTE === */
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

.text-gradient {
  background: var(--gradient-fusion);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.text-primary {
  color: var(--seamless-blue);
}
.text-secondary {
  color: var(--gray-600);
}
.text-muted {
  color: var(--gray-500);
}
.text-white {
  color: var(--white);
}

/* === CLASSES UTILITAIRES FOND === */
.bg-gradient {
  background: var(--gradient-fusion);
}
.bg-gradient-seamless {
  background: var(--gradient-seamless);
}
.bg-gradient-edomatch {
  background: var(--gradient-edomatch);
}
.bg-secondary {
  background: var(--bg-secondary);
}
.bg-tertiary {
  background: var(--bg-tertiary);
}
.bg-dark {
  background: var(--bg-dark);
  color: var(--white);
}

/* === CLASSES UTILITAIRES ESPACEMENT === */
.m-0 {
  margin: 0;
}
.mt-0 {
  margin-top: 0;
}
.mr-0 {
  margin-right: 0;
}
.mb-0 {
  margin-bottom: 0;
}
.ml-0 {
  margin-left: 0;
}

.m-1 {
  margin: var(--spacing-1);
}
.mt-1 {
  margin-top: var(--spacing-1);
}
.mr-1 {
  margin-right: var(--spacing-1);
}
.mb-1 {
  margin-bottom: var(--spacing-1);
}
.ml-1 {
  margin-left: var(--spacing-1);
}

.m-2 {
  margin: var(--spacing-2);
}
.mt-2 {
  margin-top: var(--spacing-2);
}
.mr-2 {
  margin-right: var(--spacing-2);
}
.mb-2 {
  margin-bottom: var(--spacing-2);
}
.ml-2 {
  margin-left: var(--spacing-2);
}

.m-4 {
  margin: var(--spacing-4);
}
.mt-4 {
  margin-top: var(--spacing-4);
}
.mr-4 {
  margin-right: var(--spacing-4);
}
.mb-4 {
  margin-bottom: var(--spacing-4);
}
.ml-4 {
  margin-left: var(--spacing-4);
}

.m-6 {
  margin: var(--spacing-6);
}
.mt-6 {
  margin-top: var(--spacing-6);
}
.mr-6 {
  margin-right: var(--spacing-6);
}
.mb-6 {
  margin-bottom: var(--spacing-6);
}
.ml-6 {
  margin-left: var(--spacing-6);
}

.m-8 {
  margin: var(--spacing-8);
}
.mt-8 {
  margin-top: var(--spacing-8);
}
.mr-8 {
  margin-right: var(--spacing-8);
}
.mb-8 {
  margin-bottom: var(--spacing-8);
}
.ml-8 {
  margin-left: var(--spacing-8);
}

/* === CLASSES UTILITAIRES DISPLAY === */
.d-none {
  display: none;
}
.d-block {
  display: block;
}
.d-inline {
  display: inline;
}
.d-inline-block {
  display: inline-block;
}
.d-flex {
  display: flex;
}
.d-grid {
  display: grid;
}

.flex-col {
  flex-direction: column;
}
.flex-row {
  flex-direction: row;
}
.items-center {
  align-items: center;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}

/* === RESPONSIVE UTILITIES === */
@media (max-width: 768px) {
  .md-hidden {
    display: none;
  }
  .md-block {
    display: block;
  }
  .md-flex {
    display: flex;
  }
}

@media (min-width: 769px) {
  .lg-hidden {
    display: none;
  }
  .lg-block {
    display: block;
  }
  .lg-flex {
    display: flex;
  }
}

/* === ANIMATIONS ET KEYFRAMES === */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes bounce {
  0%,
  20%,
  53%,
  80%,
  100% {
    transform: translateY(0);
  }
  40%,
  43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
  90% {
    transform: translateY(-2px);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

// Classes d'animation
.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}
.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}
.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}
.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}
.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}
.animate-pulse {
  animation: pulse 2s infinite;
}
.animate-bounce {
  animation: bounce 1s infinite;
}
.animate-spin {
  animation: spin 1s linear infinite;
}
.animate-float {
  animation: float 3s ease-in-out infinite;
}

// Délais d'animation
.animate-delay-100 {
  animation-delay: 100ms;
}
.animate-delay-200 {
  animation-delay: 200ms;
}
.animate-delay-300 {
  animation-delay: 300ms;
}
.animate-delay-500 {
  animation-delay: 500ms;
}
.animate-delay-700 {
  animation-delay: 700ms;
}
.animate-delay-1000 {
  animation-delay: 1000ms;
}

/* === STYLES GLOBAUX FINAUX === */
// Scroll smooth pour toute l'application
html {
  scroll-behavior: smooth;
}

// Focus visible pour l'accessibilité
*:focus-visible {
  outline: 2px solid var(--seamless-blue);
  outline-offset: 2px;
}

// Sélection de texte personnalisée
::selection {
  background: var(--seamless-blue);
  color: var(--white);
}

::-moz-selection {
  background: var(--seamless-blue);
  color: var(--white);
}

// Scrollbar personnalisée (Webkit)
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-400);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-500);
}

// Styles pour les images
img {
  max-width: 100%;
  height: auto;
}

// Styles pour les listes
ul,
ol {
  padding-left: var(--spacing-6);
}

li {
  margin-bottom: var(--spacing-1);
}

// Styles pour les formulaires
input,
textarea,
select {
  font-family: inherit;
  font-size: inherit;
}

// Styles pour les boutons
button {
  font-family: inherit;
  cursor: pointer;
}

/* === FIN DU SYSTÈME DE DESIGN === */
// Le système de design est maintenant complet et prêt pour les composants
