/* Système de design global - Fusion SeamlessHR x Edomatch */

// Import Google Fonts
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap");

// Variables de couleurs principales (fusion des deux sites)
:root {
  // Couleurs primaires (inspirées de SeamlessHR)
  --primary-blue: #1e40af;
  --primary-blue-light: #3b82f6;
  --primary-blue-dark: #1e3a8a;

  // Couleurs secondaires (inspirées d'Edomatch)
  --accent-purple: #7c3aed;
  --accent-purple-light: #a855f7;
  --accent-green: #10b981;
  --accent-orange: #f59e0b;

  // Couleurs neutres
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  // Couleurs de fond
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-dark: #0f172a;
  --bg-gradient: linear-gradient(
    135deg,
    var(--primary-blue) 0%,
    var(--accent-purple) 100%
  );
  --bg-gradient-light: linear-gradient(
    135deg,
    var(--primary-blue-light) 0%,
    var(--accent-purple-light) 100%
  );

  // Typographie
  --font-primary: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-secondary: "Poppins", sans-serif;

  // Tailles de police
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  // Espacements
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  --spacing-4xl: 6rem;

  // Bordures et rayons
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  // Ombres
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);

  // Transitions
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

// Reset et styles de base
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

// Typographie globale
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-secondary);
  font-weight: 600;
  line-height: 1.2;
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
}

h1 {
  font-size: var(--text-5xl);
  font-weight: 700;

  @media (max-width: 768px) {
    font-size: var(--text-4xl);
  }
}

h2 {
  font-size: var(--text-4xl);

  @media (max-width: 768px) {
    font-size: var(--text-3xl);
  }
}

h3 {
  font-size: var(--text-3xl);

  @media (max-width: 768px) {
    font-size: var(--text-2xl);
  }
}

h4 {
  font-size: var(--text-2xl);
}

h5 {
  font-size: var(--text-xl);
}

h6 {
  font-size: var(--text-lg);
}

p {
  margin-bottom: var(--spacing-md);
  color: var(--gray-600);
  line-height: 1.7;
}

a {
  color: var(--primary-blue);
  text-decoration: none;
  transition: color var(--transition-fast);

  &:hover {
    color: var(--primary-blue-dark);
  }
}

// Classes utilitaires
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);

  @media (max-width: 768px) {
    padding: 0 var(--spacing-md);
  }
}

.container-fluid {
  width: 100%;
  padding: 0 var(--spacing-lg);

  @media (max-width: 768px) {
    padding: 0 var(--spacing-md);
  }
}

// Section générique
.section {
  padding: var(--spacing-4xl) 0;

  @media (max-width: 768px) {
    padding: var(--spacing-3xl) 0;
  }
}

.section-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);

  .section-title {
    font-size: var(--text-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-md);

    @media (max-width: 768px) {
      font-size: var(--text-3xl);
    }
  }

  .section-subtitle {
    font-size: var(--text-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

// Boutons
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing-xl);
  font-size: var(--text-base);
  font-weight: 500;
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  text-decoration: none;
  white-space: nowrap;
  font-family: var(--font-primary);

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.btn-primary {
  background: var(--bg-gradient);
  color: white;
  box-shadow: var(--shadow-md);

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: var(--bg-gradient-light);
  }
}

.btn-secondary {
  background: white;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);

  &:hover:not(:disabled) {
    background: var(--primary-blue);
    color: white;
  }
}

.btn-outline {
  background: transparent;
  color: var(--gray-700);
  border: 2px solid var(--gray-300);

  &:hover:not(:disabled) {
    border-color: var(--primary-blue);
    color: var(--primary-blue);
  }
}

.btn-lg {
  padding: var(--spacing-lg) var(--spacing-2xl);
  font-size: var(--text-lg);
}

.btn-sm {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--text-sm);
}

// Grille responsive
.grid {
  display: grid;
  gap: var(--spacing-xl);
}

.grid-2 {
  grid-template-columns: repeat(2, 1fr);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.grid-3 {
  grid-template-columns: repeat(3, 1fr);

  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.grid-4 {
  grid-template-columns: repeat(4, 1fr);

  @media (max-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

// Cartes
.card {
  background: var(--bg-primary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-200);

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
  }
}

.card-header {
  margin-bottom: var(--spacing-lg);

  .card-title {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
  }

  .card-subtitle {
    color: var(--gray-600);
    font-size: var(--text-sm);
  }
}

.card-body {
  margin-bottom: var(--spacing-lg);

  p {
    color: var(--gray-600);
    line-height: 1.6;
  }
}

.card-footer {
  border-top: 1px solid var(--gray-200);
  padding-top: var(--spacing-md);
}

// Animations et effets
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

// Classes utilitaires pour le texte
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-gradient {
  background: var(--bg-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-primary {
  color: var(--primary-blue);
}

.text-secondary {
  color: var(--gray-600);
}

.text-muted {
  color: var(--gray-500);
}

// Classes utilitaires pour les espacements
.mb-0 {
  margin-bottom: 0;
}
.mb-1 {
  margin-bottom: var(--spacing-xs);
}
.mb-2 {
  margin-bottom: var(--spacing-sm);
}
.mb-3 {
  margin-bottom: var(--spacing-md);
}
.mb-4 {
  margin-bottom: var(--spacing-lg);
}
.mb-5 {
  margin-bottom: var(--spacing-xl);
}
.mb-6 {
  margin-bottom: var(--spacing-2xl);
}

.mt-0 {
  margin-top: 0;
}
.mt-1 {
  margin-top: var(--spacing-xs);
}
.mt-2 {
  margin-top: var(--spacing-sm);
}
.mt-3 {
  margin-top: var(--spacing-md);
}
.mt-4 {
  margin-top: var(--spacing-lg);
}
.mt-5 {
  margin-top: var(--spacing-xl);
}
.mt-6 {
  margin-top: var(--spacing-2xl);
}

// Classes pour les fonds
.bg-gradient {
  background: var(--bg-gradient);
}

.bg-secondary {
  background: var(--bg-secondary);
}

.bg-dark {
  background: var(--bg-dark);
  color: white;
}

// Classes pour les bordures
.border-radius {
  border-radius: var(--radius-lg);
}

.border-radius-xl {
  border-radius: var(--radius-xl);
}

// Responsive utilities
.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-flex {
  display: flex;
}

.d-grid {
  display: grid;
}

@media (max-width: 768px) {
  .d-md-none {
    display: none;
  }

  .d-md-block {
    display: block;
  }

  .d-md-flex {
    display: flex;
  }
}

@media (min-width: 769px) {
  .d-lg-none {
    display: none;
  }

  .d-lg-block {
    display: block;
  }

  .d-lg-flex {
    display: flex;
  }
}
